"""
Database Model Analysis for HMS
Comprehensive analysis of Django models, relationships, and database design
"""

import os
import sys
import django
from datetime import datetime
from django.apps import apps
from django.db import models, connection
from django.core.exceptions import ValidationError

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()


class DatabaseModelAnalyzer:
    """
    Comprehensive database model analyzer
    """
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.model_stats = {}
        self.relationship_map = {}
    
    def log_issue(self, category, model_name, message, severity='ERROR'):
        """Log a model issue"""
        issue = {
            'category': category,
            'model': model_name,
            'message': message,
            'severity': severity,
            'timestamp': datetime.now()
        }
        
        if severity == 'ERROR':
            self.issues.append(issue)
        else:
            self.warnings.append(issue)
    
    def analyze_model_structure(self):
        """Analyze model structure and design"""
        print("🏗️ Analyzing Model Structure...")
        
        hms_apps = [
            'users', 'patient_management', 'staff_management', 
            'appointment_system', 'medical_system', 'billing_system',
            'inventory', 'communications', 'emergency_system', 'ai_services'
        ]
        
        for app_name in hms_apps:
            try:
                app_config = apps.get_app_config(app_name)
                models_list = app_config.get_models()
                
                self.model_stats[app_name] = {
                    'model_count': len(models_list),
                    'models': []
                }
                
                for model in models_list:
                    self.analyze_individual_model(model, app_name)
                    
            except Exception as e:
                self.log_issue('APP_ERROR', app_name, f"Error analyzing app: {str(e)}")
    
    def analyze_individual_model(self, model, app_name):
        """Analyze individual model"""
        model_name = model.__name__
        
        # Basic model info
        model_info = {
            'name': model_name,
            'fields': [],
            'relationships': [],
            'indexes': [],
            'constraints': []
        }
        
        # Analyze fields
        for field in model._meta.get_fields():
            field_info = self.analyze_field(field, model_name)
            if field_info:
                model_info['fields'].append(field_info)
        
        # Check for required fields
        self.check_required_fields(model, model_name)
        
        # Check for proper indexing
        self.check_model_indexing(model, model_name)
        
        # Check for proper constraints
        self.check_model_constraints(model, model_name)
        
        # Check for proper string representation
        self.check_string_representation(model, model_name)
        
        self.model_stats[app_name]['models'].append(model_info)
    
    def analyze_field(self, field, model_name):
        """Analyze individual field"""
        field_info = {
            'name': field.name,
            'type': field.__class__.__name__,
            'nullable': getattr(field, 'null', False),
            'blank': getattr(field, 'blank', False),
            'unique': getattr(field, 'unique', False),
            'indexed': getattr(field, 'db_index', False)
        }
        
        # Check for relationship fields
        if isinstance(field, (models.ForeignKey, models.OneToOneField, models.ManyToManyField)):
            field_info['relationship'] = True
            field_info['related_model'] = field.related_model.__name__
            
            # Check for proper on_delete behavior
            if isinstance(field, (models.ForeignKey, models.OneToOneField)):
                if not hasattr(field, 'on_delete'):
                    self.log_issue('MISSING_ON_DELETE', model_name,
                                 f"Field '{field.name}' missing on_delete parameter")
                elif field.on_delete == models.CASCADE:
                    # Warn about CASCADE usage
                    self.log_issue('CASCADE_WARNING', model_name,
                                 f"Field '{field.name}' uses CASCADE delete - verify this is intentional", 'WARNING')
        
        # Check for proper field validation
        if isinstance(field, models.CharField):
            if not hasattr(field, 'max_length') or field.max_length is None:
                self.log_issue('MISSING_MAX_LENGTH', model_name,
                             f"CharField '{field.name}' missing max_length")
        
        # Check for email fields
        if 'email' in field.name.lower() and not isinstance(field, models.EmailField):
            self.log_issue('EMAIL_FIELD_TYPE', model_name,
                         f"Field '{field.name}' should use EmailField", 'WARNING')
        
        # Check for phone number fields
        if 'phone' in field.name.lower() and isinstance(field, models.CharField):
            if not field.max_length or field.max_length < 15:
                self.log_issue('PHONE_FIELD_LENGTH', model_name,
                             f"Phone field '{field.name}' should have max_length >= 15", 'WARNING')
        
        return field_info
    
    def check_required_fields(self, model, model_name):
        """Check for required fields in models"""
        field_names = [f.name for f in model._meta.get_fields()]
        
        # Check for timestamp fields
        if 'created_at' not in field_names:
            self.log_issue('MISSING_TIMESTAMP', model_name,
                         "Model missing 'created_at' timestamp field", 'WARNING')
        
        if 'updated_at' not in field_names:
            self.log_issue('MISSING_TIMESTAMP', model_name,
                         "Model missing 'updated_at' timestamp field", 'WARNING')
        
        # Check for ID fields in specific models
        if 'Patient' in model_name and 'patient_id' not in field_names:
            self.log_issue('MISSING_ID_FIELD', model_name,
                         "Patient model missing 'patient_id' field")
        
        if 'Staff' in model_name and 'employee_id' not in field_names:
            self.log_issue('MISSING_ID_FIELD', model_name,
                         "Staff model missing 'employee_id' field")
        
        if 'Appointment' in model_name and 'appointment_id' not in field_names:
            self.log_issue('MISSING_ID_FIELD', model_name,
                         "Appointment model missing 'appointment_id' field")
    
    def check_model_indexing(self, model, model_name):
        """Check for proper database indexing"""
        # Check if frequently queried fields have indexes
        field_names = [f.name for f in model._meta.get_fields()]
        
        # Common fields that should be indexed
        should_be_indexed = []
        
        if 'email' in field_names:
            should_be_indexed.append('email')
        
        if 'username' in field_names:
            should_be_indexed.append('username')
        
        if 'patient_id' in field_names:
            should_be_indexed.append('patient_id')
        
        if 'employee_id' in field_names:
            should_be_indexed.append('employee_id')
        
        if 'appointment_id' in field_names:
            should_be_indexed.append('appointment_id')
        
        for field_name in should_be_indexed:
            field = model._meta.get_field(field_name)
            if not getattr(field, 'db_index', False) and not getattr(field, 'unique', False):
                self.log_issue('MISSING_INDEX', model_name,
                             f"Field '{field_name}' should be indexed", 'WARNING')
    
    def check_model_constraints(self, model, model_name):
        """Check for proper model constraints"""
        # Check Meta class
        if hasattr(model, '_meta') and hasattr(model._meta, 'constraints'):
            constraints = model._meta.constraints
            if not constraints:
                # Check if model should have constraints
                field_names = [f.name for f in model._meta.get_fields()]
                
                # Models that should have unique constraints
                if 'patient_id' in field_names or 'employee_id' in field_names:
                    self.log_issue('MISSING_CONSTRAINTS', model_name,
                                 "Model should have unique constraints", 'WARNING')
        
        # Check for proper unique_together
        if hasattr(model._meta, 'unique_together') and model._meta.unique_together:
            # This is deprecated in favor of constraints
            self.log_issue('DEPRECATED_UNIQUE_TOGETHER', model_name,
                         "unique_together is deprecated, use constraints instead", 'WARNING')
    
    def check_string_representation(self, model, model_name):
        """Check for proper string representation"""
        if not hasattr(model, '__str__'):
            self.log_issue('MISSING_STR_METHOD', model_name,
                         "Model missing __str__ method", 'WARNING')
    
    def analyze_relationships(self):
        """Analyze model relationships"""
        print("🔗 Analyzing Model Relationships...")
        
        all_models = apps.get_models()
        
        for model in all_models:
            model_name = model.__name__
            app_label = model._meta.app_label
            
            if app_label not in ['users', 'patient_management', 'staff_management', 
                               'appointment_system', 'medical_system', 'billing_system',
                               'inventory', 'communications', 'emergency_system', 'ai_services']:
                continue
            
            relationships = []
            
            for field in model._meta.get_fields():
                if isinstance(field, (models.ForeignKey, models.OneToOneField)):
                    relationships.append({
                        'type': 'ForeignKey' if isinstance(field, models.ForeignKey) else 'OneToOne',
                        'field': field.name,
                        'related_model': field.related_model.__name__,
                        'on_delete': getattr(field, 'on_delete', None)
                    })
                elif isinstance(field, models.ManyToManyField):
                    relationships.append({
                        'type': 'ManyToMany',
                        'field': field.name,
                        'related_model': field.related_model.__name__
                    })
            
            if relationships:
                self.relationship_map[model_name] = relationships
    
    def check_circular_dependencies(self):
        """Check for circular dependencies in relationships"""
        print("🔄 Checking Circular Dependencies...")
        
        # This is a simplified check - in practice, you'd want a more sophisticated algorithm
        for model_name, relationships in self.relationship_map.items():
            for rel in relationships:
                related_model = rel['related_model']
                
                # Check if related model has a relationship back to this model
                if related_model in self.relationship_map:
                    related_relationships = self.relationship_map[related_model]
                    for related_rel in related_relationships:
                        if related_rel['related_model'] == model_name:
                            self.log_issue('CIRCULAR_DEPENDENCY', model_name,
                                         f"Potential circular dependency with {related_model}", 'WARNING')
    
    def analyze_database_performance(self):
        """Analyze database performance considerations"""
        print("⚡ Analyzing Database Performance...")
        
        with connection.cursor() as cursor:
            # Check table sizes (PostgreSQL specific - adapt for other databases)
            try:
                cursor.execute("""
                    SELECT 
                        schemaname,
                        tablename,
                        attname,
                        n_distinct,
                        correlation
                    FROM pg_stats 
                    WHERE schemaname = 'public'
                    ORDER BY tablename;
                """)
                
                stats = cursor.fetchall()
                if stats:
                    print(f"  ✓ Found statistics for {len(stats)} table columns")
                else:
                    self.log_issue('NO_DB_STATS', 'DATABASE',
                                 "No database statistics available", 'WARNING')
                    
            except Exception as e:
                # Not PostgreSQL or stats not available
                self.log_issue('DB_STATS_ERROR', 'DATABASE',
                             f"Could not retrieve database statistics: {str(e)}", 'WARNING')
    
    def generate_model_report(self):
        """Generate comprehensive model analysis report"""
        print("\n" + "=" * 80)
        print("🏗️ DATABASE MODEL ANALYSIS REPORT")
        print("=" * 80)
        
        print(f"Total Issues Found: {len(self.issues)}")
        print(f"Total Warnings: {len(self.warnings)}")
        
        if self.issues:
            print("\n🚨 CRITICAL MODEL ISSUES:")
            for issue in self.issues:
                print(f"  [{issue['category']}] {issue['model']}: {issue['message']}")
        
        if self.warnings:
            print("\n⚠️ MODEL WARNINGS:")
            for warning in self.warnings:
                print(f"  [{warning['category']}] {warning['model']}: {warning['message']}")
        
        print("\n📊 MODEL STATISTICS:")
        for app_name, stats in self.model_stats.items():
            print(f"  {app_name}: {stats['model_count']} models")
        
        print(f"\n🔗 RELATIONSHIP SUMMARY:")
        print(f"  Models with relationships: {len(self.relationship_map)}")
        
        print("\n📋 RECOMMENDATIONS:")
        print("  1. Add proper indexes to frequently queried fields")
        print("  2. Implement proper constraints for data integrity")
        print("  3. Add timestamp fields to all models")
        print("  4. Use proper field types (EmailField, etc.)")
        print("  5. Implement __str__ methods for all models")
        print("  6. Review CASCADE delete behaviors")
        print("  7. Consider database normalization opportunities")
        
        print("=" * 80)
        
        return len(self.issues) == 0
    
    def run_full_analysis(self):
        """Run complete database model analysis"""
        print("🏗️ Starting Database Model Analysis...")
        print("-" * 60)
        
        self.analyze_model_structure()
        self.analyze_relationships()
        self.check_circular_dependencies()
        self.analyze_database_performance()
        
        success = self.generate_model_report()
        
        if success:
            print("✅ Database model analysis PASSED")
        else:
            print("❌ Database model analysis found issues")
        
        return success


def main():
    """Main function to run database model analysis"""
    analyzer = DatabaseModelAnalyzer()
    success = analyzer.run_full_analysis()
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
