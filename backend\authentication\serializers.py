from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password

User = get_user_model()


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration
    """
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'password', 'password_confirm',
            'first_name', 'last_name', 'role', 'phone_number',
            'date_of_birth', 'address', 'emergency_contact'
        ]
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user


class UserLoginSerializer(serializers.Serializer):
    """
    Serializer for user login
    """
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for user profile with role-based properties
    """
    full_name = serializers.ReadOnlyField()
    is_admin = serializers.SerializerMethodField()
    is_doctor = serializers.SerializerMethodField()
    is_nurse = serializers.SerializerMethodField()
    is_patient = serializers.SerializerMethodField()
    is_receptionist = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'full_name', 'role', 'phone_number', 'date_of_birth',
            'address', 'emergency_contact', 'is_active', 'date_joined',
            'is_admin', 'is_doctor', 'is_nurse', 'is_patient', 'is_receptionist'
        ]
        read_only_fields = [
            'id', 'username', 'date_joined', 'is_admin', 'is_doctor',
            'is_nurse', 'is_patient', 'is_receptionist'
        ]

    def get_is_admin(self, obj):
        return obj.is_admin

    def get_is_doctor(self, obj):
        print(f"DEBUG: get_is_doctor called for {obj.username}, role={obj.role}, is_doctor={obj.is_doctor}")
        return obj.is_doctor

    def get_is_nurse(self, obj):
        return obj.is_nurse

    def get_is_patient(self, obj):
        return obj.is_patient

    def get_is_receptionist(self, obj):
        return obj.is_receptionist


class UserUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user profile
    """
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'email', 'phone_number',
            'date_of_birth', 'address', 'emergency_contact'
        ]
