from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema, OpenApiResponse
from .serializers import UserRegistrationSerializer, UserLoginSerializer, UserProfileSerializer

User = get_user_model()


@extend_schema(
    request=UserRegistrationSerializer,
    responses={
        201: UserProfileSerializer,
        400: OpenApiResponse(description='Validation errors'),
        403: OpenApiResponse(description='Admin access required')
    },
    description="Register a new user account (Admin only)",
    summary="User Registration (Admin Only)"
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def register(request):
    """
    Register a new user - Only accessible by admin users
    """
    # Check if the requesting user has admin privileges
    from hms.common_utils import PermissionUtils
    if not PermissionUtils.user_can_create_users(request.user):
        return Response({
            'error': 'Only administrators can register new users'
        }, status=status.HTTP_403_FORBIDDEN)

    serializer = UserRegistrationSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.save()

        # Don't automatically generate tokens for admin-created users
        # They should login with their credentials
        return Response({
            'message': 'User registered successfully',
            'user': UserProfileSerializer(user).data,
        }, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    request=UserLoginSerializer,
    responses={
        200: UserProfileSerializer,
        401: OpenApiResponse(description='Invalid credentials'),
        400: OpenApiResponse(description='Validation errors')
    },
    description="Login user and return JWT tokens",
    summary="User Login"
)
@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    """
    Login user and return JWT tokens
    """
    serializer = UserLoginSerializer(data=request.data)
    if serializer.is_valid():
        username = serializer.validated_data['username']
        password = serializer.validated_data['password']

        user = authenticate(username=username, password=password)
        if user:
            refresh = RefreshToken.for_user(user)

            return Response({
                'message': 'Login successful',
                'user': UserProfileSerializer(user).data,
                'tokens': {
                    'refresh': str(refresh),
                    'access': str(refresh.access_token),
                }
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'error': 'Invalid credentials'
            }, status=status.HTTP_401_UNAUTHORIZED)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    request={'refresh': str},
    responses={
        200: OpenApiResponse(description='Logout successful'),
        400: OpenApiResponse(description='Invalid token')
    },
    description="Logout user by blacklisting the refresh token",
    summary="User Logout"
)
@api_view(['POST'])
def logout(request):
    """
    Logout user by blacklisting the refresh token
    """
    try:
        refresh_token = request.data["refresh"]
        token = RefreshToken(refresh_token)
        token.blacklist()

        return Response({
            'message': 'Logout successful'
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({
            'error': 'Invalid token'
        }, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    responses={
        200: UserProfileSerializer,
        401: OpenApiResponse(description='Authentication required')
    },
    description="Get current user profile information",
    summary="User Profile"
)
@api_view(['GET'])
def profile(request):
    """
    Get current user profile
    """
    serializer = UserProfileSerializer(request.user)
    return Response(serializer.data, status=status.HTTP_200_OK)
