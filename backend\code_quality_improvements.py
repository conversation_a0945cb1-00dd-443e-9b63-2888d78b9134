"""
Code Quality Improvements for HMS
Identifies and suggests improvements for code quality, consistency, and maintainability
"""

import os
import sys
import ast
import re
from pathlib import Path
from datetime import datetime


class CodeQualityAnalyzer:
    """
    Comprehensive code quality analyzer for HMS
    """
    
    def __init__(self, backend_path=".", frontend_path="../frontend/src"):
        self.backend_path = Path(backend_path)
        self.frontend_path = Path(frontend_path)
        self.issues = []
        self.suggestions = []
        self.stats = {}
    
    def log_issue(self, category, file_path, message, severity='WARNING', line_number=None):
        """Log a code quality issue"""
        issue = {
            'category': category,
            'file': str(file_path),
            'message': message,
            'severity': severity,
            'line': line_number,
            'timestamp': datetime.now()
        }
        
        if severity == 'ERROR':
            self.issues.append(issue)
        else:
            self.suggestions.append(issue)
    
    def analyze_python_code_quality(self):
        """Analyze Python code quality"""
        print("🐍 Analyzing Python Code Quality...")
        
        python_files = list(self.backend_path.rglob("*.py"))
        self.stats['python_files'] = len(python_files)
        
        for py_file in python_files:
            if self.should_skip_file(py_file):
                continue
            
            self.analyze_python_file(py_file)
    
    def should_skip_file(self, file_path):
        """Check if file should be skipped"""
        skip_patterns = [
            '__pycache__',
            'migrations',
            'venv',
            'env',
            '.git',
            'node_modules',
            'test_',  # Skip our test files
            'manage.py'
        ]
        
        return any(pattern in str(file_path) for pattern in skip_patterns)
    
    def analyze_python_file(self, file_path):
        """Analyze individual Python file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # Parse AST for deeper analysis
            try:
                tree = ast.parse(content)
                self.analyze_ast(tree, file_path)
            except SyntaxError as e:
                self.log_issue('SYNTAX_ERROR', file_path, f"Syntax error: {str(e)}", 'ERROR')
            
            # Line-by-line analysis
            self.analyze_python_lines(lines, file_path)
            
        except Exception as e:
            self.log_issue('FILE_READ_ERROR', file_path, f"Could not read file: {str(e)}")
    
    def analyze_ast(self, tree, file_path):
        """Analyze Python AST for code quality issues"""
        class QualityVisitor(ast.NodeVisitor):
            def __init__(self, analyzer, file_path):
                self.analyzer = analyzer
                self.file_path = file_path
                self.function_complexity = {}
                self.class_methods = {}
            
            def visit_FunctionDef(self, node):
                # Check function complexity (simplified cyclomatic complexity)
                complexity = self.calculate_complexity(node)
                if complexity > 10:
                    self.analyzer.log_issue('HIGH_COMPLEXITY', self.file_path,
                                          f"Function '{node.name}' has high complexity ({complexity})",
                                          line_number=node.lineno)
                
                # Check function length
                if hasattr(node, 'end_lineno') and node.end_lineno:
                    length = node.end_lineno - node.lineno
                    if length > 50:
                        self.analyzer.log_issue('LONG_FUNCTION', self.file_path,
                                              f"Function '{node.name}' is too long ({length} lines)",
                                              line_number=node.lineno)
                
                # Check for missing docstrings
                if not ast.get_docstring(node):
                    if not node.name.startswith('_'):  # Skip private methods
                        self.analyzer.log_issue('MISSING_DOCSTRING', self.file_path,
                                              f"Function '{node.name}' missing docstring",
                                              line_number=node.lineno)
                
                self.generic_visit(node)
            
            def visit_ClassDef(self, node):
                # Check for missing docstrings
                if not ast.get_docstring(node):
                    self.analyzer.log_issue('MISSING_DOCSTRING', self.file_path,
                                          f"Class '{node.name}' missing docstring",
                                          line_number=node.lineno)
                
                # Count methods
                methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
                if len(methods) > 20:
                    self.analyzer.log_issue('LARGE_CLASS', self.file_path,
                                          f"Class '{node.name}' has many methods ({len(methods)})",
                                          line_number=node.lineno)
                
                self.generic_visit(node)
            
            def calculate_complexity(self, node):
                """Calculate simplified cyclomatic complexity"""
                complexity = 1  # Base complexity
                
                for child in ast.walk(node):
                    if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                        complexity += 1
                    elif isinstance(child, ast.ExceptHandler):
                        complexity += 1
                    elif isinstance(child, ast.BoolOp):
                        complexity += len(child.values) - 1
                
                return complexity
        
        visitor = QualityVisitor(self, file_path)
        visitor.visit(tree)
    
    def analyze_python_lines(self, lines, file_path):
        """Analyze Python file line by line"""
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # Check line length
            if len(line) > 120:
                self.log_issue('LONG_LINE', file_path,
                             f"Line too long ({len(line)} characters)", line_number=i)
            
            # Check for TODO/FIXME comments
            if any(keyword in line.upper() for keyword in ['TODO', 'FIXME', 'HACK', 'XXX']):
                self.log_issue('TODO_COMMENT', file_path,
                             f"TODO/FIXME comment found", line_number=i)
            
            # Check for print statements (should use logging)
            if 'print(' in line and 'test' not in str(file_path).lower():
                self.log_issue('PRINT_STATEMENT', file_path,
                             f"Print statement found - consider using logging", line_number=i)
            
            # Check for bare except clauses
            if line.strip() == 'except:':
                self.log_issue('BARE_EXCEPT', file_path,
                             f"Bare except clause - specify exception type", line_number=i)
            
            # Check for hardcoded strings that might be constants
            if re.search(r'["\'][A-Z_]{3,}["\']', line) and 'import' not in line:
                self.log_issue('HARDCODED_CONSTANT', file_path,
                             f"Potential hardcoded constant", line_number=i)
    
    def analyze_django_specific_quality(self):
        """Analyze Django-specific code quality"""
        print("🎯 Analyzing Django-Specific Quality...")
        
        # Check models
        models_files = list(self.backend_path.rglob("models.py"))
        for model_file in models_files:
            self.analyze_django_models(model_file)
        
        # Check views
        views_files = list(self.backend_path.rglob("views.py"))
        for view_file in views_files:
            self.analyze_django_views(view_file)
        
        # Check serializers
        serializer_files = list(self.backend_path.rglob("serializers.py"))
        for serializer_file in serializer_files:
            self.analyze_django_serializers(serializer_file)
    
    def analyze_django_models(self, file_path):
        """Analyze Django models for quality issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # Check for missing Meta classes
            if 'class ' in content and 'models.Model' in content:
                if 'class Meta:' not in content:
                    self.log_issue('MISSING_META_CLASS', file_path,
                                 "Model missing Meta class for ordering/verbose_name")
            
            # Check for missing __str__ methods
            class_lines = [i for i, line in enumerate(lines) if 'class ' in line and 'models.Model' in line]
            for class_line in class_lines:
                # Look for __str__ method in the next 50 lines
                str_method_found = False
                for j in range(class_line, min(class_line + 50, len(lines))):
                    if 'def __str__' in lines[j]:
                        str_method_found = True
                        break
                    if 'class ' in lines[j] and j > class_line:
                        break
                
                if not str_method_found:
                    self.log_issue('MISSING_STR_METHOD', file_path,
                                 "Model missing __str__ method", line_number=class_line + 1)
            
        except Exception as e:
            self.log_issue('FILE_READ_ERROR', file_path, f"Could not analyze models: {str(e)}")
    
    def analyze_django_views(self, file_path):
        """Analyze Django views for quality issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # Check for missing permission classes
            if 'ViewSet' in content or 'APIView' in content:
                if 'permission_classes' not in content:
                    self.log_issue('MISSING_PERMISSIONS', file_path,
                                 "View missing permission_classes")
            
            # Check for missing serializer classes in ViewSets
            if 'ViewSet' in content and 'serializer_class' not in content:
                self.log_issue('MISSING_SERIALIZER', file_path,
                             "ViewSet missing serializer_class")
            
            # Check for hardcoded HTTP status codes
            for i, line in enumerate(lines, 1):
                if re.search(r'status=\d{3}', line):
                    self.log_issue('HARDCODED_STATUS', file_path,
                                 "Hardcoded HTTP status code - use status constants", line_number=i)
            
        except Exception as e:
            self.log_issue('FILE_READ_ERROR', file_path, f"Could not analyze views: {str(e)}")
    
    def analyze_django_serializers(self, file_path):
        """Analyze Django serializers for quality issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # Check for missing Meta classes in serializers
            if 'Serializer' in content and 'class Meta:' not in content:
                self.log_issue('MISSING_SERIALIZER_META', file_path,
                             "Serializer missing Meta class")
            
            # Check for missing field validation
            if 'def validate_' not in content and 'Serializer' in content:
                self.log_issue('NO_FIELD_VALIDATION', file_path,
                             "Serializer has no custom field validation")
            
        except Exception as e:
            self.log_issue('FILE_READ_ERROR', file_path, f"Could not analyze serializers: {str(e)}")
    
    def check_import_organization(self):
        """Check import organization and unused imports"""
        print("📦 Checking Import Organization...")
        
        python_files = list(self.backend_path.rglob("*.py"))
        
        for py_file in python_files:
            if self.should_skip_file(py_file):
                continue
            
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                
                # Check import order (simplified)
                import_lines = [i for i, line in enumerate(lines) if line.strip().startswith(('import ', 'from '))]
                
                if import_lines:
                    # Check for imports after code
                    first_non_import = None
                    for i, line in enumerate(lines):
                        if (line.strip() and 
                            not line.strip().startswith(('import ', 'from ', '#', '"""', "'''")) and
                            not line.strip() == ''):
                            first_non_import = i
                            break
                    
                    if first_non_import:
                        late_imports = [i for i in import_lines if i > first_non_import]
                        if late_imports:
                            self.log_issue('LATE_IMPORTS', py_file,
                                         f"Imports found after code at lines {late_imports}")
                
            except Exception as e:
                self.log_issue('FILE_READ_ERROR', py_file, f"Could not check imports: {str(e)}")
    
    def generate_quality_report(self):
        """Generate comprehensive quality report"""
        print("\n" + "=" * 80)
        print("📊 CODE QUALITY ANALYSIS REPORT")
        print("=" * 80)
        
        print(f"Files Analyzed: {self.stats.get('python_files', 0)}")
        print(f"Quality Issues: {len(self.issues)}")
        print(f"Suggestions: {len(self.suggestions)}")
        
        if self.issues:
            print("\n🚨 QUALITY ISSUES:")
            for issue in self.issues:
                line_info = f" (Line {issue['line']})" if issue['line'] else ""
                print(f"  [{issue['category']}] {issue['file']}{line_info}")
                print(f"    {issue['message']}")
        
        if self.suggestions:
            print("\n💡 QUALITY SUGGESTIONS:")
            for suggestion in self.suggestions:
                line_info = f" (Line {suggestion['line']})" if suggestion['line'] else ""
                print(f"  [{suggestion['category']}] {suggestion['file']}{line_info}")
                print(f"    {suggestion['message']}")
        
        print("\n📋 QUALITY IMPROVEMENT RECOMMENDATIONS:")
        print("  1. Add docstrings to all public functions and classes")
        print("  2. Break down complex functions into smaller ones")
        print("  3. Use logging instead of print statements")
        print("  4. Specify exception types in except clauses")
        print("  5. Move hardcoded constants to settings or constants file")
        print("  6. Add proper Meta classes to Django models")
        print("  7. Ensure all views have appropriate permission classes")
        print("  8. Organize imports according to PEP 8 standards")
        print("  9. Add field validation to serializers")
        print("  10. Use HTTP status constants instead of hardcoded numbers")
        
        print("=" * 80)
        
        return len(self.issues) == 0
    
    def run_full_analysis(self):
        """Run complete code quality analysis"""
        print("📊 Starting Code Quality Analysis...")
        print("-" * 60)
        
        self.analyze_python_code_quality()
        self.analyze_django_specific_quality()
        self.check_import_organization()
        
        success = self.generate_quality_report()
        
        if success:
            print("✅ Code quality analysis completed - no critical issues")
        else:
            print("⚠️ Code quality analysis found issues to address")
        
        return success


def main():
    """Main function to run code quality analysis"""
    analyzer = CodeQualityAnalyzer()
    success = analyzer.run_full_analysis()
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
