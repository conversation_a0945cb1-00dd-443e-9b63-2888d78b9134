"""
Base views and mixins for HMS application
Consolidates common patterns to reduce code duplication
"""
from rest_framework import viewsets, status, filters
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q


class BaseViewSet(viewsets.ModelViewSet):
    """
    Base ViewSet with common functionality for all HMS ViewSets
    """
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    
    def get_success_response(self, data, message=None, status_code=status.HTTP_200_OK):
        """
        Standardized success response format
        """
        response_data = {
            'success': True,
            'data': data
        }
        if message:
            response_data['message'] = message
        return Response(response_data, status=status_code)
    
    def get_error_response(self, message, status_code=status.HTTP_400_BAD_REQUEST, errors=None):
        """
        Standardized error response format
        """
        response_data = {
            'success': False,
            'message': message
        }
        if errors:
            response_data['errors'] = errors
        return Response(response_data, status=status_code)


class RoleBasedFilterMixin:
    """
    Mixin to handle role-based queryset filtering
    """
    
    def filter_queryset_by_role(self, queryset):
        """
        Filter queryset based on user role
        Override this method in child classes for specific filtering logic
        """
        from hms.common_utils import PermissionUtils
        user = self.request.user

        # Check role by the role field directly instead of properties
        if hasattr(user, 'role'):
            if user.role == 'patient':
                return self.filter_for_patient(queryset, user)
            elif user.role == 'doctor':
                return self.filter_for_doctor(queryset, user)
            elif user.role == 'receptionist':
                return self.filter_for_receptionist(queryset, user)
            elif user.role == 'admin' or PermissionUtils.has_admin_access(user):
                return self.filter_for_admin(queryset, user)

        # Fallback to property-based checking
        if hasattr(user, 'is_patient') and user.is_patient:
            return self.filter_for_patient(queryset, user)
        elif hasattr(user, 'is_doctor') and user.is_doctor:
            return self.filter_for_doctor(queryset, user)
        elif hasattr(user, 'is_receptionist') and user.is_receptionist:
            return self.filter_for_receptionist(queryset, user)
        elif PermissionUtils.has_admin_access(user):
            return self.filter_for_admin(queryset, user)

        return queryset
    
    def filter_for_patient(self, queryset, user):
        """Override in child classes"""
        return queryset
    
    def filter_for_doctor(self, queryset, user):
        """Override in child classes"""
        return queryset
    
    def filter_for_receptionist(self, queryset, user):
        """Override in child classes"""
        return queryset
    
    def filter_for_admin(self, queryset, user):
        """Override in child classes"""
        return queryset


class StatusUpdateMixin:
    """
    Mixin for common status update operations
    """
    
    def update_status(self, instance, new_status, success_message=None):
        """
        Generic status update method
        """
        instance.status = new_status
        instance.save()
        
        message = success_message or f"Status updated to {new_status}"
        return self.get_success_response(
            data={'status': new_status},
            message=message
        )


class CommonPermissionMixin:
    """
    Mixin for common permission checks
    """
    
    def check_object_permission(self, obj, user):
        """
        Check if user has permission to access object
        Override in child classes for specific permission logic
        """
        return True
    
    def check_create_permission(self, user, data):
        """
        Check if user has permission to create object
        """
        return True
    
    def check_update_permission(self, obj, user, data):
        """
        Check if user has permission to update object
        """
        return True
    
    def check_delete_permission(self, obj, user):
        """
        Check if user has permission to delete object
        """
        return True
