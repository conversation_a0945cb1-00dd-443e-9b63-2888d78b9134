"""
Common utilities for HMS application
Consolidates repeated utility functions to reduce code duplication
"""
import re
from datetime import datetime, date, time
from django.core.exceptions import ValidationError
from django.utils import timezone
from rest_framework.response import Response
from rest_framework import status


class ValidationUtils:
    """
    Common validation utilities
    """
    
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        email_regex = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
        if not re.match(email_regex, email):
            raise ValidationError("Invalid email format")
        return email.lower().strip()
    
    @staticmethod
    def validate_phone_number(phone):
        """Validate phone number format"""
        phone_regex = r'^[\+]?[1-9][\d]{0,15}$'
        clean_phone = re.sub(r'[\s\-\(\)]', '', phone)
        if not re.match(phone_regex, clean_phone):
            raise ValidationError("Invalid phone number format")
        return clean_phone
    
    @staticmethod
    def validate_patient_id(patient_id):
        """Validate patient ID format (P000XXX)"""
        patient_id_regex = r'^P\d{6}$'
        if not re.match(patient_id_regex, patient_id):
            raise ValidationError("Patient ID must be in format P000XXX")
        return patient_id
    
    @staticmethod
    def validate_appointment_date(appointment_date):
        """Validate appointment date is in the future"""
        if isinstance(appointment_date, str):
            appointment_date = datetime.strptime(appointment_date, '%Y-%m-%d').date()
        
        if appointment_date < timezone.now().date():
            raise ValidationError("Appointment date must be in the future")
        return appointment_date
    
    @staticmethod
    def validate_time_format(time_str):
        """Validate time format (HH:MM)"""
        time_regex = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
        if not re.match(time_regex, time_str):
            raise ValidationError("Time must be in HH:MM format")
        return time_str


class ResponseUtils:
    """
    Common response formatting utilities
    """
    
    @staticmethod
    def success_response(data=None, message=None, status_code=status.HTTP_200_OK):
        """
        Standardized success response
        """
        response_data = {'success': True}
        
        if data is not None:
            response_data['data'] = data
        if message:
            response_data['message'] = message
            
        return Response(response_data, status=status_code)
    
    @staticmethod
    def error_response(message, status_code=status.HTTP_400_BAD_REQUEST, errors=None):
        """
        Standardized error response
        """
        response_data = {
            'success': False,
            'message': message
        }
        
        if errors:
            response_data['errors'] = errors
            
        return Response(response_data, status=status_code)
    
    @staticmethod
    def paginated_response(queryset, serializer_class, request, message=None):
        """
        Standardized paginated response
        """
        from django.core.paginator import Paginator
        
        page_size = int(request.GET.get('page_size', 20))
        page_number = int(request.GET.get('page', 1))
        
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page_number)
        
        serializer = serializer_class(page_obj, many=True)
        
        response_data = {
            'success': True,
            'data': {
                'results': serializer.data,
                'count': paginator.count,
                'next': page_obj.has_next(),
                'previous': page_obj.has_previous(),
                'page': page_number,
                'total_pages': paginator.num_pages
            }
        }
        
        if message:
            response_data['message'] = message
            
        return Response(response_data)


class QueryUtils:
    """
    Common query utilities
    """
    
    @staticmethod
    def filter_by_date_range(queryset, date_field, start_date=None, end_date=None):
        """
        Filter queryset by date range
        """
        if start_date:
            filter_kwargs = {f"{date_field}__gte": start_date}
            queryset = queryset.filter(**filter_kwargs)
        
        if end_date:
            filter_kwargs = {f"{date_field}__lte": end_date}
            queryset = queryset.filter(**filter_kwargs)
            
        return queryset
    
    @staticmethod
    def search_in_fields(queryset, search_term, fields):
        """
        Search for term in multiple fields
        """
        from django.db.models import Q
        
        if not search_term:
            return queryset
            
        query = Q()
        for field in fields:
            query |= Q(**{f"{field}__icontains": search_term})
            
        return queryset.filter(query)


class DateTimeUtils:
    """
    Common date/time utilities
    """
    
    @staticmethod
    def format_date(date_obj, format_type='medium'):
        """
        Format date for display
        """
        if not date_obj:
            return None
            
        formats = {
            'short': '%m/%d/%Y',
            'medium': '%b %d, %Y',
            'long': '%B %d, %Y'
        }
        
        return date_obj.strftime(formats.get(format_type, formats['medium']))
    
    @staticmethod
    def format_time(time_obj, format_12h=True):
        """
        Format time for display
        """
        if not time_obj:
            return None
            
        if format_12h:
            return time_obj.strftime('%I:%M %p')
        else:
            return time_obj.strftime('%H:%M')
    
    @staticmethod
    def parse_date_string(date_string):
        """
        Parse date string to date object
        """
        try:
            return datetime.strptime(date_string, '%Y-%m-%d').date()
        except ValueError:
            raise ValidationError("Invalid date format. Use YYYY-MM-DD")
    
    @staticmethod
    def parse_time_string(time_string):
        """
        Parse time string to time object
        """
        try:
            return datetime.strptime(time_string, '%H:%M').time()
        except ValueError:
            raise ValidationError("Invalid time format. Use HH:MM")


class PermissionUtils:
    """
    Centralized permission utilities for HMS
    Handles both HMS admin roles and Django superuser permissions
    """

    @staticmethod
    def has_admin_access(user):
        """
        Check if user has admin access (HMS admin OR Django superuser)
        Use this for general admin permission checks
        """
        return (hasattr(user, 'has_admin_privileges') and user.has_admin_privileges) or \
               (hasattr(user, 'is_admin') and user.is_admin) or \
               (hasattr(user, 'is_superuser') and user.is_superuser)

    @staticmethod
    def has_hms_admin_access(user):
        """
        Check if user has HMS business admin access (role='admin')
        Use this for HMS-specific admin features
        """
        return hasattr(user, 'is_admin') and user.is_admin

    @staticmethod
    def has_technical_admin_access(user):
        """
        Check if user has Django technical admin access (is_superuser=True)
        Use this for Django admin interface and technical operations
        """
        return hasattr(user, 'is_superuser') and user.is_superuser

    @staticmethod
    def user_can_access_patient_data(user, patient):
        """
        Check if user can access patient data
        """
        if PermissionUtils.has_admin_access(user):
            return True
        if hasattr(user, 'is_doctor') and user.is_doctor:
            return True
        if hasattr(user, 'is_patient') and user.is_patient:
            return patient.user == user
        if hasattr(user, 'is_receptionist') and user.is_receptionist:
            return True
        return False
    
    @staticmethod
    def user_can_modify_appointment(user, appointment):
        """
        Check if user can modify appointment
        """
        if PermissionUtils.has_admin_access(user):
            return True
        if hasattr(user, 'is_doctor') and user.is_doctor:
            return appointment.doctor == user
        if hasattr(user, 'is_patient') and user.is_patient:
            return appointment.patient.user == user
        if hasattr(user, 'is_receptionist') and user.is_receptionist:
            return True
        return False

    @staticmethod
    def user_can_create_users(user):
        """
        Check if user can create new users
        Only HMS admins and Django superusers can create users
        """
        return PermissionUtils.has_admin_access(user)

    @staticmethod
    def user_can_access_django_admin(user):
        """
        Check if user can access Django admin interface
        Only Django superusers can access Django admin
        """
        return PermissionUtils.has_technical_admin_access(user)

    @staticmethod
    def user_can_manage_system_settings(user):
        """
        Check if user can manage system settings
        Only HMS admins can manage HMS system settings
        """
        return PermissionUtils.has_hms_admin_access(user)
