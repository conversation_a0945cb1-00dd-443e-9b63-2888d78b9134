"""
Centralized Permission Classes for HMS
Provides consistent permission checking across all views
"""

from rest_framework.permissions import BasePermission
from .common_utils import PermissionUtils


class IsHMSAdmin(BasePermission):
    """
    Permission class for HMS business admin access
    Allows access only to users with role='admin'
    """
    message = "Only HMS administrators can perform this action."

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            PermissionUtils.has_hms_admin_access(request.user)
        )


class IsTechnicalAdmin(BasePermission):
    """
    Permission class for Django technical admin access
    Allows access only to users with is_superuser=True
    """
    message = "Only technical administrators can perform this action."

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            PermissionUtils.has_technical_admin_access(request.user)
        )


class IsAnyAdmin(BasePermission):
    """
    Permission class for any admin access (HMS admin OR Django superuser)
    Allows access to users with role='admin' OR is_superuser=True
    """
    message = "Administrator privileges required."

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            PermissionUtils.has_admin_access(request.user)
        )


class IsDoctor(BasePermission):
    """
    Permission class for doctor access
    """
    message = "Doctor privileges required."

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            hasattr(request.user, 'is_doctor') and 
            request.user.is_doctor
        )


class IsNurse(BasePermission):
    """
    Permission class for nurse access
    """
    message = "Nurse privileges required."

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            hasattr(request.user, 'is_nurse') and 
            request.user.is_nurse
        )


class IsReceptionist(BasePermission):
    """
    Permission class for receptionist access
    """
    message = "Receptionist privileges required."

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            hasattr(request.user, 'is_receptionist') and 
            request.user.is_receptionist
        )


class IsPatient(BasePermission):
    """
    Permission class for patient access
    """
    message = "Patient privileges required."

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            hasattr(request.user, 'is_patient') and 
            request.user.is_patient
        )


class IsMedicalStaff(BasePermission):
    """
    Permission class for medical staff (doctors, nurses, admins)
    """
    message = "Medical staff privileges required."

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            (PermissionUtils.has_admin_access(request.user) or
             (hasattr(request.user, 'is_doctor') and request.user.is_doctor) or
             (hasattr(request.user, 'is_nurse') and request.user.is_nurse))
        )


class CanAccessPatientData(BasePermission):
    """
    Permission class for patient data access
    Checks if user can access patient data based on role and relationship
    """
    message = "Insufficient privileges to access patient data."

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # For patient objects, check if user can access this specific patient
        if hasattr(obj, 'user'):  # Patient model
            return PermissionUtils.user_can_access_patient_data(request.user, obj)
        elif hasattr(obj, 'patient'):  # Related models (appointments, prescriptions, etc.)
            return PermissionUtils.user_can_access_patient_data(request.user, obj.patient)
        
        # For other objects, allow if user has general patient data access
        return (
            PermissionUtils.has_admin_access(request.user) or
            (hasattr(request.user, 'is_doctor') and request.user.is_doctor) or
            (hasattr(request.user, 'is_nurse') and request.user.is_nurse) or
            (hasattr(request.user, 'is_receptionist') and request.user.is_receptionist)
        )


class CanModifyAppointments(BasePermission):
    """
    Permission class for appointment modification
    """
    message = "Insufficient privileges to modify appointments."

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        return PermissionUtils.user_can_modify_appointment(request.user, obj)


class CanCreateUsers(BasePermission):
    """
    Permission class for user creation
    Only admins can create new users
    """
    message = "Only administrators can create new users."

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            PermissionUtils.user_can_create_users(request.user)
        )


class CanManageSystemSettings(BasePermission):
    """
    Permission class for system settings management
    Only HMS admins can manage system settings
    """
    message = "Only HMS administrators can manage system settings."

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            PermissionUtils.user_can_manage_system_settings(request.user)
        )


# Convenience permission combinations
class IsAdminOrDoctor(BasePermission):
    """
    Permission class for admin or doctor access
    """
    message = "Administrator or doctor privileges required."

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            (PermissionUtils.has_admin_access(request.user) or
             (hasattr(request.user, 'is_doctor') and request.user.is_doctor))
        )


class IsAdminOrReceptionist(BasePermission):
    """
    Permission class for admin or receptionist access
    """
    message = "Administrator or receptionist privileges required."

    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            (PermissionUtils.has_admin_access(request.user) or
             (hasattr(request.user, 'is_receptionist') and request.user.is_receptionist))
        )


class IsOwnerOrMedicalStaff(BasePermission):
    """
    Permission class for object owner or medical staff access
    Allows access if user owns the object or is medical staff
    """
    message = "You can only access your own data or you must be medical staff."

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Check if user owns the object
        if hasattr(obj, 'user') and obj.user == request.user:
            return True
        
        # Check if user is medical staff
        return (
            PermissionUtils.has_admin_access(request.user) or
            (hasattr(request.user, 'is_doctor') and request.user.is_doctor) or
            (hasattr(request.user, 'is_nurse') and request.user.is_nurse) or
            (hasattr(request.user, 'is_receptionist') and request.user.is_receptionist)
        )
