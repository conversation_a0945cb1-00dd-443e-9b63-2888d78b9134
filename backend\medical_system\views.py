from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count
from django.utils import timezone

from hms.permissions import IsAnyAdmin, IsMedicalStaff, CanAccessPatientData
from hms.common_utils import PermissionUtils
from .models import Department, Ward, Bed, Admission
from .serializers import (
    DepartmentSerializer, WardSerializer, BedSerializer,
    AdmissionSerializer, AdmissionCreateSerializer
)


class DepartmentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing departments
    Medical staff can view departments, admins can manage them
    """
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [IsMedicalStaff]  # Medical staff and admins can access
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description', 'location']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def perform_create(self, serializer):
        # Only admins can create departments
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can create departments")
        serializer.save()

    def perform_update(self, serializer):
        # Only admins can update departments
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can update departments")
        serializer.save()

    def perform_destroy(self, instance):
        # Only admins can delete departments
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can delete departments")
        instance.delete()

    def get_queryset(self):
        return Department.objects.select_related('head_of_department')

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get active departments"""
        queryset = self.get_queryset().filter(is_active=True)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def with_stats(self, request):
        """Get departments with statistics"""
        queryset = self.get_queryset().annotate(
            ward_count=Count('wards'),
            bed_count=Count('wards__beds'),
            occupied_beds=Count('wards__beds', filter=Q(wards__beds__is_occupied=True))
        )

        data = []
        for dept in queryset:
            dept_data = self.get_serializer(dept).data
            dept_data['ward_count'] = dept.ward_count
            dept_data['bed_count'] = dept.bed_count
            dept_data['occupied_beds'] = dept.occupied_beds
            dept_data['available_beds'] = dept.bed_count - dept.occupied_beds
            data.append(dept_data)

        return Response(data)


class WardViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing wards
    Medical staff can view wards, admins can manage them
    """
    queryset = Ward.objects.all()
    serializer_class = WardSerializer
    permission_classes = [IsMedicalStaff]  # Medical staff and admins can access
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'ward_type', 'is_active']
    search_fields = ['name', 'ward_number', 'department__name']
    ordering_fields = ['name', 'ward_number', 'created_at']
    ordering = ['department__name', 'ward_number']

    def get_queryset(self):
        return Ward.objects.select_related('department')

    def perform_create(self, serializer):
        # Only admins can create wards
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can create wards")
        serializer.save()

    def perform_update(self, serializer):
        # Only admins can update wards
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can update wards")
        serializer.save()

    def perform_destroy(self, instance):
        # Only admins can delete wards
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can delete wards")
        instance.delete()

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get active wards"""
        queryset = self.get_queryset().filter(is_active=True)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_department(self, request):
        """Get wards by department"""
        department_id = request.query_params.get('department_id')
        if department_id:
            queryset = self.get_queryset().filter(department_id=department_id)
        else:
            queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class BedViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing beds
    Medical staff can view beds, admins can manage them
    """
    queryset = Bed.objects.all()
    serializer_class = BedSerializer
    permission_classes = [IsMedicalStaff]  # Medical staff and admins can access
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['ward', 'bed_type', 'status']
    search_fields = ['bed_number', 'ward__name', 'ward__department__name']
    ordering_fields = ['bed_number', 'created_at']
    ordering = ['ward__name', 'bed_number']

    def get_queryset(self):
        return Bed.objects.select_related('ward__department')

    def perform_create(self, serializer):
        # Only admins can create beds
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can create beds")
        serializer.save()

    def perform_update(self, serializer):
        # Only admins can update beds
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can update beds")
        serializer.save()

    def perform_destroy(self, instance):
        # Only admins can delete beds
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can delete beds")
        instance.delete()

    @action(detail=False, methods=['get'])
    def available(self, request):
        """Get available beds"""
        queryset = self.get_queryset().filter(status='available')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_ward(self, request):
        """Get beds by ward"""
        ward_id = request.query_params.get('ward_id')
        if ward_id:
            queryset = self.get_queryset().filter(ward_id=ward_id)
        else:
            queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """Get beds by type"""
        bed_type = request.query_params.get('bed_type')
        if bed_type:
            queryset = self.get_queryset().filter(bed_type=bed_type)
        else:
            queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def occupy(self, request, pk=None):
        """Mark bed as occupied"""
        bed = self.get_object()
        bed.status = 'occupied'
        bed.save()
        return Response({'status': 'Bed marked as occupied'})

    @action(detail=True, methods=['post'])
    def vacate(self, request, pk=None):
        """Mark bed as vacant"""
        bed = self.get_object()
        bed.status = 'available'
        bed.current_patient = None
        bed.save()
        return Response({'status': 'Bed marked as vacant'})


class AdmissionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing admissions
    Medical staff can access admissions with patient data restrictions
    """
    queryset = Admission.objects.all()
    permission_classes = [CanAccessPatientData]  # Patient data access control
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['patient', 'bed', 'admitting_doctor', 'status']
    search_fields = ['admission_id', 'patient__patient_id', 'patient__user__first_name', 'patient__user__last_name']
    ordering_fields = ['admission_date', 'discharge_date', 'created_at']
    ordering = ['-admission_date']

    def get_serializer_class(self):
        if self.action == 'create':
            return AdmissionCreateSerializer
        return AdmissionSerializer

    def get_queryset(self):
        queryset = Admission.objects.select_related(
            'patient__user', 'bed__ward__department', 'admitting_doctor'
        )

        # Apply role-based filtering for patient data access
        user = self.request.user
        if hasattr(user, 'is_patient') and user.is_patient:
            # Patients can only see their own admissions
            queryset = queryset.filter(patient__user=user)
        elif hasattr(user, 'is_doctor') and user.is_doctor:
            # Doctors can see admissions they're involved with
            queryset = queryset.filter(admitting_doctor=user)
        elif not PermissionUtils.has_admin_access(user):
            # Other medical staff can see all admissions (nurses, receptionists)
            pass

        return queryset

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get active admissions"""
        queryset = self.get_queryset().filter(status='admitted')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def discharge(self, request, pk=None):
        """Discharge patient"""
        admission = self.get_object()
        discharge_date = request.data.get('discharge_date', timezone.now().date())
        discharge_summary = request.data.get('discharge_summary', '')

        admission.discharge_date = discharge_date
        admission.discharge_summary = discharge_summary
        admission.status = 'discharged'
        admission.save()

        # Mark bed as vacant
        bed = admission.bed
        bed.status = 'available'
        bed.current_patient = None
        bed.save()

        return Response({'status': 'Patient discharged successfully'})

    @action(detail=False, methods=['get'])
    def by_doctor(self, request):
        """Get admissions by doctor"""
        doctor_id = request.query_params.get('doctor_id')
        if doctor_id:
            queryset = self.get_queryset().filter(admitting_doctor_id=doctor_id)
        else:
            queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
