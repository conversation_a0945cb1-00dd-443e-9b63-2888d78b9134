from rest_framework import status, filters, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q

from hms.base_views import BaseViewSet, RoleBasedFilterMixin, StatusUpdateMixin
from hms.common_utils import ResponseUtils, PermissionUtils
from .models import Patient, MedicalRecord, Prescription, LabTest, MedicalDocument
from .serializers import (
    PatientSerializer, PatientCreateSerializer, PatientDetailSerializer,
    MedicalRecordSerializer, PrescriptionSerializer, LabTestSerializer,
    MedicalDocumentSerializer
)


class PatientViewSet(BaseViewSet, RoleBasedFilterMixin):
    """
    ViewSet for managing patients
    """
    queryset = Patient.objects.all()
    filterset_fields = ['blood_group', 'insurance_provider']
    search_fields = ['patient_id', 'user__first_name', 'user__last_name', 'user__email']
    ordering_fields = ['created_at', 'patient_id']
    ordering = ['-created_at']

    def get_serializer_class(self):
        if self.action == 'create':
            return PatientCreateSerializer
        elif self.action == 'retrieve':
            return PatientDetailSerializer
        return PatientSerializer

    def get_queryset(self):
        queryset = Patient.objects.select_related('user').prefetch_related(
            'medical_records', 'prescriptions', 'lab_tests', 'medical_documents'
        )
        return self.filter_queryset_by_role(queryset)

    def filter_for_patient(self, queryset, user):
        """Patients can only see their own data"""
        return queryset.filter(user=user)

    def filter_for_doctor(self, queryset, user):
        """Doctors can see all patients for medical consultation and treatment"""
        # In a hospital setting, doctors need access to all patients for:
        # - Emergency consultations
        # - New patient assignments
        # - Cross-departmental consultations
        # - General medical practice
        print(f"DEBUG: filter_for_doctor called for user {user.username} with role {user.role}")
        print(f"DEBUG: Original queryset count: {queryset.count()}")
        return queryset

    def filter_for_receptionist(self, queryset, user):
        """Receptionists can see all patients"""
        return queryset

    def filter_for_admin(self, queryset, user):
        """Admins can see all patients"""
        return queryset

    @action(detail=True, methods=['get'])
    def medical_history(self, request, pk=None):
        """
        Get complete medical history for a patient
        """
        patient = self.get_object()
        medical_records = MedicalRecord.objects.filter(patient=patient).order_by('-visit_date')
        serializer = MedicalRecordSerializer(medical_records, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def prescriptions(self, request, pk=None):
        """
        Get all prescriptions for a patient
        """
        patient = self.get_object()
        prescriptions = Prescription.objects.filter(patient=patient).order_by('-prescribed_date')
        serializer = PrescriptionSerializer(prescriptions, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def lab_tests(self, request, pk=None):
        """
        Get all lab tests for a patient
        """
        patient = self.get_object()
        lab_tests = LabTest.objects.filter(patient=patient).order_by('-ordered_date')
        serializer = LabTestSerializer(lab_tests, many=True)
        return Response(serializer.data)


class MedicalRecordViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing medical records
    """
    queryset = MedicalRecord.objects.all()
    serializer_class = MedicalRecordSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['patient', 'doctor', 'follow_up_required']
    search_fields = ['chief_complaint', 'diagnosis']
    ordering_fields = ['visit_date', 'created_at']
    ordering = ['-visit_date']

    def get_queryset(self):
        queryset = MedicalRecord.objects.select_related('patient__user', 'doctor')

        # Filter based on user role
        if self.request.user.is_patient:
            queryset = queryset.filter(patient__user=self.request.user)
        elif self.request.user.is_doctor:
            queryset = queryset.filter(doctor=self.request.user)

        return queryset

    def perform_create(self, serializer):
        serializer.save(doctor=self.request.user)


class PrescriptionViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing prescriptions
    """
    queryset = Prescription.objects.all()
    serializer_class = PrescriptionSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['patient', 'doctor', 'status']
    search_fields = ['medication_name', 'prescription_id']
    ordering_fields = ['prescribed_date', 'start_date', 'end_date']
    ordering = ['-prescribed_date']

    def get_queryset(self):
        queryset = Prescription.objects.select_related('patient__user', 'doctor', 'medical_record')

        # Filter based on user role
        if self.request.user.is_patient:
            queryset = queryset.filter(patient__user=self.request.user)
        elif self.request.user.is_doctor:
            queryset = queryset.filter(doctor=self.request.user)

        return queryset

    def perform_create(self, serializer):
        serializer.save(doctor=self.request.user)

    @action(detail=True, methods=['post'])
    def mark_completed(self, request, pk=None):
        """
        Mark prescription as completed
        """
        prescription = self.get_object()
        prescription.status = 'completed'
        prescription.save()
        return Response({'status': 'Prescription marked as completed'})


class LabTestViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing lab tests
    """
    queryset = LabTest.objects.all()
    serializer_class = LabTestSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['patient', 'doctor', 'test_type', 'status', 'result_status']
    search_fields = ['test_name', 'test_id']
    ordering_fields = ['ordered_date', 'result_date']
    ordering = ['-ordered_date']

    def get_queryset(self):
        queryset = LabTest.objects.select_related('patient__user', 'doctor', 'medical_record')

        # Filter based on user role
        if self.request.user.is_patient:
            queryset = queryset.filter(patient__user=self.request.user)
        elif self.request.user.is_doctor:
            queryset = queryset.filter(doctor=self.request.user)

        return queryset

    def perform_create(self, serializer):
        serializer.save(doctor=self.request.user)

    @action(detail=True, methods=['post'])
    def update_result(self, request, pk=None):
        """
        Update lab test results
        """
        lab_test = self.get_object()

        # Only allow lab technicians or doctors to update results
        from hms.common_utils import PermissionUtils
        if not (self.request.user.is_doctor or
                self.request.user.role == 'lab_technician' or
                PermissionUtils.has_admin_access(self.request.user)):
            return Response(
                {'error': 'Only doctors, lab technicians, or admins can update results'},
                status=status.HTTP_403_FORBIDDEN
            )

        result_data = request.data
        for field in ['result_value', 'result_unit', 'reference_range', 'result_status', 'notes']:
            if field in result_data:
                setattr(lab_test, field, result_data[field])

        lab_test.status = 'completed'
        lab_test.save()

        serializer = self.get_serializer(lab_test)
        return Response(serializer.data)


class MedicalDocumentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing medical documents
    """
    queryset = MedicalDocument.objects.all()
    serializer_class = MedicalDocumentSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['patient', 'document_type', 'uploaded_by']
    search_fields = ['title', 'description']
    ordering_fields = ['created_at']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = MedicalDocument.objects.select_related('patient__user', 'uploaded_by', 'medical_record')

        # Filter based on user role
        if self.request.user.is_patient:
            queryset = queryset.filter(patient__user=self.request.user)

        return queryset

    def perform_create(self, serializer):
        serializer.save(uploaded_by=self.request.user)
