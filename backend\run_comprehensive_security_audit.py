"""
Comprehensive Security Audit Runner for HMS
Executes all security, authentication, and workflow tests
"""

import os
import sys
import django
import subprocess
import time
from datetime import datetime

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()

from django.core.management import execute_from_command_line
from django.db import connection
from django.test.utils import get_runner
from django.conf import settings


class ComprehensiveSecurityAudit:
    """
    Comprehensive security audit for HMS application
    """
    
    def __init__(self):
        self.start_time = datetime.now()
        self.results = {
            'role_based_access': None,
            'authentication_security': None,
            'end_to_end_workflows': None,
            'database_integrity': None,
            'permission_consistency': None
        }
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
    
    def print_header(self):
        """Print audit header"""
        print("=" * 80)
        print("🔒 HMS COMPREHENSIVE SECURITY AUDIT")
        print("=" * 80)
        print(f"Started at: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Audit Level: Enterprise (30-year experienced developer standards)")
        print("=" * 80)
    
    def run_role_based_access_tests(self):
        """Run role-based access control tests"""
        print("\n🔐 RUNNING ROLE-BASED ACCESS CONTROL TESTS...")
        print("-" * 60)
        
        try:
            # Import and run the test
            from test_role_based_access_control import run_role_based_tests
            success = run_role_based_tests()
            self.results['role_based_access'] = success
            
            if success:
                print("✅ Role-Based Access Control: PASSED")
                self.passed_tests += 1
            else:
                print("❌ Role-Based Access Control: FAILED")
                self.failed_tests += 1
                
        except Exception as e:
            print(f"❌ Role-Based Access Control: ERROR - {str(e)}")
            self.results['role_based_access'] = False
            self.failed_tests += 1
        
        self.total_tests += 1
    
    def run_authentication_security_tests(self):
        """Run authentication and security tests"""
        print("\n🔑 RUNNING AUTHENTICATION & SECURITY TESTS...")
        print("-" * 60)
        
        try:
            from test_authentication_security import run_authentication_security_tests
            success = run_authentication_security_tests()
            self.results['authentication_security'] = success
            
            if success:
                print("✅ Authentication & Security: PASSED")
                self.passed_tests += 1
            else:
                print("❌ Authentication & Security: FAILED")
                self.failed_tests += 1
                
        except Exception as e:
            print(f"❌ Authentication & Security: ERROR - {str(e)}")
            self.results['authentication_security'] = False
            self.failed_tests += 1
        
        self.total_tests += 1
    
    def run_end_to_end_workflow_tests(self):
        """Run end-to-end workflow tests"""
        print("\n🔄 RUNNING END-TO-END WORKFLOW TESTS...")
        print("-" * 60)
        
        try:
            from test_end_to_end_workflows import run_end_to_end_tests
            success = run_end_to_end_tests()
            self.results['end_to_end_workflows'] = success
            
            if success:
                print("✅ End-to-End Workflows: PASSED")
                self.passed_tests += 1
            else:
                print("❌ End-to-End Workflows: FAILED")
                self.failed_tests += 1
                
        except Exception as e:
            print(f"❌ End-to-End Workflows: ERROR - {str(e)}")
            self.results['end_to_end_workflows'] = False
            self.failed_tests += 1
        
        self.total_tests += 1
    
    def run_database_integrity_tests(self):
        """Run database integrity tests"""
        print("\n🗄️ RUNNING DATABASE INTEGRITY TESTS...")
        print("-" * 60)

        try:
            # Run database integrity validation
            from validate_database_integrity import DatabaseIntegrityValidator
            validator = DatabaseIntegrityValidator()
            success = validator.run_full_validation()
            self.results['database_integrity'] = success

            if success:
                print("✅ Database Integrity: PASSED")
                self.passed_tests += 1
            else:
                print("❌ Database Integrity: FAILED")
                self.failed_tests += 1

        except Exception as e:
            print(f"❌ Database Integrity: ERROR - {str(e)}")
            self.results['database_integrity'] = False
            self.failed_tests += 1

        self.total_tests += 1

    def run_database_model_analysis(self):
        """Run database model analysis"""
        print("\n🏗️ RUNNING DATABASE MODEL ANALYSIS...")
        print("-" * 60)

        try:
            from analyze_database_models import DatabaseModelAnalyzer
            analyzer = DatabaseModelAnalyzer()
            success = analyzer.run_full_analysis()

            if success:
                print("✅ Database Model Analysis: PASSED")
                self.passed_tests += 1
            else:
                print("❌ Database Model Analysis: ISSUES FOUND")
                self.failed_tests += 1

        except Exception as e:
            print(f"❌ Database Model Analysis: ERROR - {str(e)}")
            self.failed_tests += 1

        self.total_tests += 1

    def run_frontend_security_audit(self):
        """Run frontend security audit"""
        print("\n🌐 RUNNING FRONTEND SECURITY AUDIT...")
        print("-" * 60)

        try:
            # Import and run frontend security audit
            import subprocess
            result = subprocess.run([
                sys.executable, '../frontend_security_audit.py'
            ], capture_output=True, text=True, cwd=os.path.dirname(__file__))

            success = result.returncode == 0

            if success:
                print("✅ Frontend Security Audit: PASSED")
                self.passed_tests += 1
            else:
                print("❌ Frontend Security Audit: ISSUES FOUND")
                print(result.stdout)
                self.failed_tests += 1

        except Exception as e:
            print(f"❌ Frontend Security Audit: ERROR - {str(e)}")
            self.failed_tests += 1

        self.total_tests += 1
    
    def check_database_integrity(self):
        """Check database integrity and relationships"""
        from users.models import User
        from patient_management.models import Patient
        from staff_management.models import StaffProfile
        
        issues = []
        
        # Check for orphaned patients
        orphaned_patients = Patient.objects.filter(user__isnull=True).count()
        if orphaned_patients > 0:
            issues.append(f"Found {orphaned_patients} orphaned patient records")
        
        # Check for orphaned staff profiles
        orphaned_staff = StaffProfile.objects.filter(user__isnull=True).count()
        if orphaned_staff > 0:
            issues.append(f"Found {orphaned_staff} orphaned staff profiles")
        
        # Check patient ID format consistency
        invalid_patient_ids = Patient.objects.exclude(patient_id__regex=r'^P\d{6}$').count()
        if invalid_patient_ids > 0:
            issues.append(f"Found {invalid_patient_ids} patients with invalid ID format")
        
        # Check for users without proper roles
        users_without_roles = User.objects.filter(role__isnull=True).count()
        if users_without_roles > 0:
            issues.append(f"Found {users_without_roles} users without assigned roles")
        
        # Check for duplicate patient IDs
        from django.db.models import Count
        duplicate_patient_ids = Patient.objects.values('patient_id').annotate(
            count=Count('patient_id')
        ).filter(count__gt=1).count()
        if duplicate_patient_ids > 0:
            issues.append(f"Found {duplicate_patient_ids} duplicate patient IDs")
        
        if issues:
            print("Database integrity issues found:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print("✅ Database integrity check passed")
            return True
    
    def run_permission_consistency_tests(self):
        """Run permission consistency tests"""
        print("\n🛡️ RUNNING PERMISSION CONSISTENCY TESTS...")
        print("-" * 60)
        
        try:
            success = self.check_permission_consistency()
            self.results['permission_consistency'] = success
            
            if success:
                print("✅ Permission Consistency: PASSED")
                self.passed_tests += 1
            else:
                print("❌ Permission Consistency: FAILED")
                self.failed_tests += 1
                
        except Exception as e:
            print(f"❌ Permission Consistency: ERROR - {str(e)}")
            self.results['permission_consistency'] = False
            self.failed_tests += 1
        
        self.total_tests += 1
    
    def check_permission_consistency(self):
        """Check permission consistency across frontend and backend"""
        from hms.common_utils import PermissionUtils as BackendPermissionUtils
        
        # Test user creation
        from users.models import User
        admin_user = User.objects.filter(role=User.Role.ADMIN).first()
        patient_user = User.objects.filter(role=User.Role.PATIENT).first()
        
        if not admin_user or not patient_user:
            print("⚠️ No test users found, creating them...")
            admin_user = User.objects.create_user(
                username='test_admin_perm',
                email='<EMAIL>',
                password='admin123',
                role=User.Role.ADMIN
            )
            patient_user = User.objects.create_user(
                username='test_patient_perm',
                email='<EMAIL>',
                password='patient123',
                role=User.Role.PATIENT
            )
        
        issues = []
        
        # Test admin access consistency
        if not BackendPermissionUtils.has_admin_access(admin_user):
            issues.append("Backend PermissionUtils doesn't recognize admin user")
        
        if BackendPermissionUtils.has_admin_access(patient_user):
            issues.append("Backend PermissionUtils incorrectly grants admin access to patient")
        
        # Test HMS admin access
        if not BackendPermissionUtils.has_hms_admin_access(admin_user):
            issues.append("Backend PermissionUtils doesn't recognize HMS admin access")
        
        if BackendPermissionUtils.has_hms_admin_access(patient_user):
            issues.append("Backend PermissionUtils incorrectly grants HMS admin access to patient")
        
        # Test user creation permissions
        if not BackendPermissionUtils.user_can_create_users(admin_user):
            issues.append("Backend PermissionUtils doesn't allow admin to create users")
        
        if BackendPermissionUtils.user_can_create_users(patient_user):
            issues.append("Backend PermissionUtils incorrectly allows patient to create users")
        
        if issues:
            print("Permission consistency issues found:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print("✅ Permission consistency check passed")
            return True
    
    def generate_security_report(self):
        """Generate comprehensive security report"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        print("\n" + "=" * 80)
        print("🔒 HMS SECURITY AUDIT REPORT")
        print("=" * 80)
        print(f"Audit Duration: {duration}")
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.failed_tests}")
        print(f"Success Rate: {(self.passed_tests/self.total_tests)*100:.1f}%")
        print("-" * 80)
        
        # Detailed results
        print("DETAILED RESULTS:")
        for test_name, result in self.results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"  {test_name.replace('_', ' ').title()}: {status}")
        
        print("-" * 80)
        
        # Security recommendations
        if self.failed_tests > 0:
            print("🚨 CRITICAL SECURITY ISSUES DETECTED!")
            print("\nIMMEDIATE ACTIONS REQUIRED:")
            
            if not self.results.get('role_based_access'):
                print("  - Fix role-based access control vulnerabilities")
            
            if not self.results.get('authentication_security'):
                print("  - Address authentication and authorization security gaps")
            
            if not self.results.get('end_to_end_workflows'):
                print("  - Fix workflow security and data access issues")
            
            if not self.results.get('database_integrity'):
                print("  - Resolve database integrity and consistency issues")
            
            if not self.results.get('permission_consistency'):
                print("  - Fix permission system inconsistencies")
            
            print("\n⚠️ DO NOT DEPLOY TO PRODUCTION UNTIL ALL ISSUES ARE RESOLVED!")
        else:
            print("🎉 ALL SECURITY TESTS PASSED!")
            print("✅ Application meets enterprise security standards")
            print("✅ Ready for production deployment")
        
        print("=" * 80)
        
        return self.failed_tests == 0
    
    def run_full_audit(self):
        """Run complete security audit"""
        self.print_header()

        # Run all test suites
        self.run_role_based_access_tests()
        self.run_authentication_security_tests()
        self.run_end_to_end_workflow_tests()
        self.run_database_integrity_tests()
        self.run_database_model_analysis()
        self.run_permission_consistency_tests()
        self.run_frontend_security_audit()

        # Generate report
        success = self.generate_security_report()

        return success


def main():
    """Main function to run comprehensive security audit"""
    audit = ComprehensiveSecurityAudit()
    success = audit.run_full_audit()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
