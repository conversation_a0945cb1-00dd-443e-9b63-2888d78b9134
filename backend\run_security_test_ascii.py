"""
ASCII-Only HMS Security Test Runner
Windows PowerShell compatible version without any Unicode characters
"""

import os
import sys
import django
from datetime import datetime

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()


def print_header():
    """Print audit header without Unicode"""
    print("=" * 80)
    print("HMS COMPREHENSIVE SECURITY AUDIT")
    print("=" * 80)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Audit Level: Enterprise (30-year experienced developer standards)")
    print("=" * 80)


def test_basic_imports():
    """Test if all required modules can be imported"""
    print("\nTesting Basic Imports...")
    print("-" * 60)
    
    try:
        from users.models import User
        print("[PASS] Users models imported successfully")
    except Exception as e:
        print(f"[FAIL] Users models import failed: {str(e)}")
        return False
    
    try:
        from patient_management.models import Patient
        print("[PASS] Patient management models imported successfully")
    except Exception as e:
        print(f"[FAIL] Patient management models import failed: {str(e)}")
        return False
    
    try:
        from staff_management.models import StaffProfile
        print("[PASS] Staff management models imported successfully")
    except Exception as e:
        print(f"[FAIL] Staff management models import failed: {str(e)}")
        return False
    
    try:
        from hms.permissions import IsAnyAdmin, IsHMSAdmin
        print("[PASS] Permission classes imported successfully")
    except Exception as e:
        print(f"[FAIL] Permission classes import failed: {str(e)}")
        return False
    
    try:
        from hms.common_utils import PermissionUtils
        print("[PASS] Permission utilities imported successfully")
    except Exception as e:
        print(f"[FAIL] Permission utilities import failed: {str(e)}")
        return False
    
    return True


def test_permission_system():
    """Test permission system functionality"""
    print("\nTesting Permission System...")
    print("-" * 60)
    
    try:
        from users.models import User
        from hms.common_utils import PermissionUtils
        
        # Create test users
        admin_user = User.objects.filter(role=User.Role.ADMIN).first()
        if not admin_user:
            admin_user = User.objects.create_user(
                username='test_admin_security',
                email='<EMAIL>',
                password='admin123',
                role=User.Role.ADMIN,
                first_name='Test',
                last_name='Admin'
            )
            print("[PASS] Created test admin user")
        else:
            print("[PASS] Found existing admin user")
        
        patient_user = User.objects.filter(role=User.Role.PATIENT).first()
        if not patient_user:
            patient_user = User.objects.create_user(
                username='test_patient_security',
                email='<EMAIL>',
                password='patient123',
                role=User.Role.PATIENT,
                first_name='Test',
                last_name='Patient'
            )
            print("[PASS] Created test patient user")
        else:
            print("[PASS] Found existing patient user")
        
        # Test permission utilities
        if PermissionUtils.has_admin_access(admin_user):
            print("[PASS] Admin user has admin access")
        else:
            print("[FAIL] Admin user does not have admin access")
            return False
        
        if not PermissionUtils.has_admin_access(patient_user):
            print("[PASS] Patient user does not have admin access")
        else:
            print("[FAIL] Patient user incorrectly has admin access")
            return False
        
        if PermissionUtils.has_hms_admin_access(admin_user):
            print("[PASS] Admin user has HMS admin access")
        else:
            print("[FAIL] Admin user does not have HMS admin access")
            return False
        
        if not PermissionUtils.has_hms_admin_access(patient_user):
            print("[PASS] Patient user does not have HMS admin access")
        else:
            print("[FAIL] Patient user incorrectly has HMS admin access")
            return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Permission system test failed: {str(e)}")
        return False


def test_database_integrity():
    """Test basic database integrity"""
    print("\nTesting Database Integrity...")
    print("-" * 60)
    
    try:
        from users.models import User
        from patient_management.models import Patient
        from staff_management.models import StaffProfile
        
        # Check for orphaned patients
        orphaned_patients = Patient.objects.filter(user__isnull=True).count()
        if orphaned_patients == 0:
            print("[PASS] No orphaned patient records found")
        else:
            print(f"[WARN] Found {orphaned_patients} orphaned patient records")
        
        # Check for orphaned staff
        orphaned_staff = StaffProfile.objects.filter(user__isnull=True).count()
        if orphaned_staff == 0:
            print("[PASS] No orphaned staff records found")
        else:
            print(f"[WARN] Found {orphaned_staff} orphaned staff records")
        
        # Check for users without roles
        users_without_roles = User.objects.filter(role__isnull=True).count()
        if users_without_roles == 0:
            print("[PASS] All users have assigned roles")
        else:
            print(f"[WARN] Found {users_without_roles} users without roles")
        
        # Check patient ID format
        invalid_patient_ids = Patient.objects.exclude(patient_id__regex=r'^P\d{6}$').count()
        if invalid_patient_ids == 0:
            print("[PASS] All patient IDs follow correct format")
        else:
            print(f"[WARN] Found {invalid_patient_ids} patients with invalid ID format")
        
        return orphaned_patients == 0 and orphaned_staff == 0 and users_without_roles == 0
        
    except Exception as e:
        print(f"[FAIL] Database integrity test failed: {str(e)}")
        return False


def test_view_permissions():
    """Test view permission configurations"""
    print("\nTesting View Permissions...")
    print("-" * 60)
    
    try:
        # Check staff management views
        from staff_management.views import StaffProfileViewSet, ShiftViewSet
        
        # Check if StaffProfileViewSet has proper permissions
        staff_permissions = getattr(StaffProfileViewSet, 'permission_classes', [])
        if any('IsAnyAdmin' in str(perm) for perm in staff_permissions):
            print("[PASS] StaffProfileViewSet has admin permission")
        else:
            print("[FAIL] StaffProfileViewSet missing admin permission")
            return False
        
        # Check if ShiftViewSet has proper permissions
        shift_permissions = getattr(ShiftViewSet, 'permission_classes', [])
        if any('IsAnyAdmin' in str(perm) for perm in shift_permissions):
            print("[PASS] ShiftViewSet has admin permission")
        else:
            print("[FAIL] ShiftViewSet missing admin permission")
            return False
        
        # Check medical system views
        from medical_system.views import DepartmentViewSet, WardViewSet
        
        dept_permissions = getattr(DepartmentViewSet, 'permission_classes', [])
        if any('IsMedicalStaff' in str(perm) for perm in dept_permissions):
            print("[PASS] DepartmentViewSet has medical staff permission")
        else:
            print("[FAIL] DepartmentViewSet missing medical staff permission")
            return False
        
        ward_permissions = getattr(WardViewSet, 'permission_classes', [])
        if any('IsMedicalStaff' in str(perm) for perm in ward_permissions):
            print("[PASS] WardViewSet has medical staff permission")
        else:
            print("[FAIL] WardViewSet missing medical staff permission")
            return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] View permissions test failed: {str(e)}")
        return False


def test_security_fixes():
    """Test that our security fixes are properly implemented"""
    print("\nTesting Security Fixes Implementation...")
    print("-" * 60)
    
    try:
        # Test staff management security fixes
        from staff_management.views import (
            StaffProfileViewSet, LeaveRequestViewSet, PerformanceViewSet
        )
        
        # Check StaffProfileViewSet permissions
        staff_perms = StaffProfileViewSet.permission_classes
        if any('IsAnyAdmin' in str(perm.__name__) for perm in staff_perms):
            print("[PASS] StaffProfileViewSet requires admin access")
        else:
            print("[FAIL] StaffProfileViewSet does not require admin access")
            return False
        
        # Test medical system security fixes
        from medical_system.views import DepartmentViewSet, BedViewSet, AdmissionViewSet
        
        # Check DepartmentViewSet permissions
        dept_perms = DepartmentViewSet.permission_classes
        if any('IsMedicalStaff' in str(perm.__name__) for perm in dept_perms):
            print("[PASS] DepartmentViewSet requires medical staff access")
        else:
            print("[FAIL] DepartmentViewSet does not require medical staff access")
            return False
        
        # Check BedViewSet permissions
        bed_perms = BedViewSet.permission_classes
        if any('IsMedicalStaff' in str(perm.__name__) for perm in bed_perms):
            print("[PASS] BedViewSet requires medical staff access")
        else:
            print("[FAIL] BedViewSet does not require medical staff access")
            return False
        
        # Check AdmissionViewSet permissions
        admission_perms = AdmissionViewSet.permission_classes
        if any('CanAccessPatientData' in str(perm.__name__) for perm in admission_perms):
            print("[PASS] AdmissionViewSet requires patient data access permission")
        else:
            print("[FAIL] AdmissionViewSet does not require patient data access permission")
            return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Security fixes test failed: {str(e)}")
        return False


def generate_summary_report(results):
    """Generate summary report"""
    print("\n" + "=" * 80)
    print("HMS SECURITY AUDIT SUMMARY")
    print("=" * 80)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\nDETAILED RESULTS:")
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        print(f"  {test_name.replace('_', ' ').title()}: {status}")
    
    print("-" * 80)
    
    if passed_tests == total_tests:
        print("*** ALL SECURITY TESTS PASSED! ***")
        print("")
        print("SECURITY STATUS: ENTERPRISE READY")
        print("")
        print("Key Achievements:")
        print("  - Critical security fixes have been successfully implemented")
        print("  - Permission system is working correctly")
        print("  - Database integrity is maintained")
        print("  - Staff management views now require admin access")
        print("  - Medical system views now require appropriate permissions")
        print("  - Role-based access control is properly enforced")
        print("")
        print("RECOMMENDATION: Ready for production deployment")
    else:
        print("*** SECURITY ISSUES DETECTED! ***")
        print("")
        print("SECURITY STATUS: NOT READY FOR PRODUCTION")
        print("")
        print("Required Actions:")
        print("  - Review failed tests above")
        print("  - Address all security issues")
        print("  - Re-run security audit after fixes")
        print("  - Do not deploy to production until all tests pass")
    
    print("=" * 80)
    
    return passed_tests == total_tests


def main():
    """Main execution function"""
    print_header()
    
    # Run all tests
    results = {}
    
    results['basic_imports'] = test_basic_imports()
    results['permission_system'] = test_permission_system()
    results['database_integrity'] = test_database_integrity()
    results['view_permissions'] = test_view_permissions()
    results['security_fixes'] = test_security_fixes()
    
    # Generate summary
    overall_success = generate_summary_report(results)
    
    # Exit with appropriate code
    sys.exit(0 if overall_success else 1)


if __name__ == "__main__":
    main()