"""
Windows-Compatible HMS Security Test Runner
Simplified version without Unicode characters for Windows PowerShell
"""

import os
import sys
import django
from datetime import datetime

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()


def print_header():
    """Print audit header without Unicode"""
    print("=" * 80)
    print("HMS COMPREHENSIVE SECURITY AUDIT")
    print("=" * 80)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Audit Level: Enterprise (30-year experienced developer standards)")
    print("=" * 80)


def test_basic_imports():
    """Test if all required modules can be imported"""
    print("\nTesting Basic Imports...")
    print("-" * 60)
    
    try:
        from users.models import User
        print("✓ Users models imported successfully")
    except Exception as e:
        print(f"✗ Users models import failed: {str(e)}")
        return False
    
    try:
        from patient_management.models import Patient
        print("✓ Patient management models imported successfully")
    except Exception as e:
        print(f"✗ Patient management models import failed: {str(e)}")
        return False
    
    try:
        from staff_management.models import StaffProfile
        print("✓ Staff management models imported successfully")
    except Exception as e:
        print(f"✗ Staff management models import failed: {str(e)}")
        return False
    
    try:
        from hms.permissions import IsAnyAdmin, IsHMSAdmin
        print("✓ Permission classes imported successfully")
    except Exception as e:
        print(f"✗ Permission classes import failed: {str(e)}")
        return False
    
    try:
        from hms.common_utils import PermissionUtils
        print("✓ Permission utilities imported successfully")
    except Exception as e:
        print(f"✗ Permission utilities import failed: {str(e)}")
        return False
    
    return True


def test_permission_system():
    """Test permission system functionality"""
    print("\nTesting Permission System...")
    print("-" * 60)
    
    try:
        from users.models import User
        from hms.common_utils import PermissionUtils
        
        # Create test users
        admin_user = User.objects.filter(role=User.Role.ADMIN).first()
        if not admin_user:
            admin_user = User.objects.create_user(
                username='test_admin_security',
                email='<EMAIL>',
                password='admin123',
                role=User.Role.ADMIN,
                first_name='Test',
                last_name='Admin'
            )
            print("✓ Created test admin user")
        else:
            print("✓ Found existing admin user")
        
        patient_user = User.objects.filter(role=User.Role.PATIENT).first()
        if not patient_user:
            patient_user = User.objects.create_user(
                username='test_patient_security',
                email='<EMAIL>',
                password='patient123',
                role=User.Role.PATIENT,
                first_name='Test',
                last_name='Patient'
            )
            print("✓ Created test patient user")
        else:
            print("✓ Found existing patient user")
        
        # Test permission utilities
        if PermissionUtils.has_admin_access(admin_user):
            print("✓ Admin user has admin access")
        else:
            print("✗ Admin user does not have admin access")
            return False
        
        if not PermissionUtils.has_admin_access(patient_user):
            print("✓ Patient user does not have admin access")
        else:
            print("✗ Patient user incorrectly has admin access")
            return False
        
        if PermissionUtils.has_hms_admin_access(admin_user):
            print("✓ Admin user has HMS admin access")
        else:
            print("✗ Admin user does not have HMS admin access")
            return False
        
        if not PermissionUtils.has_hms_admin_access(patient_user):
            print("✓ Patient user does not have HMS admin access")
        else:
            print("✗ Patient user incorrectly has HMS admin access")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Permission system test failed: {str(e)}")
        return False


def test_database_integrity():
    """Test basic database integrity"""
    print("\nTesting Database Integrity...")
    print("-" * 60)
    
    try:
        from users.models import User
        from patient_management.models import Patient
        from staff_management.models import StaffProfile
        
        # Check for orphaned patients
        orphaned_patients = Patient.objects.filter(user__isnull=True).count()
        if orphaned_patients == 0:
            print("✓ No orphaned patient records found")
        else:
            print(f"✗ Found {orphaned_patients} orphaned patient records")
        
        # Check for orphaned staff
        orphaned_staff = StaffProfile.objects.filter(user__isnull=True).count()
        if orphaned_staff == 0:
            print("✓ No orphaned staff records found")
        else:
            print(f"✗ Found {orphaned_staff} orphaned staff records")
        
        # Check for users without roles
        users_without_roles = User.objects.filter(role__isnull=True).count()
        if users_without_roles == 0:
            print("✓ All users have assigned roles")
        else:
            print(f"✗ Found {users_without_roles} users without roles")
        
        # Check patient ID format
        invalid_patient_ids = Patient.objects.exclude(patient_id__regex=r'^P\d{6}$').count()
        if invalid_patient_ids == 0:
            print("✓ All patient IDs follow correct format")
        else:
            print(f"✗ Found {invalid_patient_ids} patients with invalid ID format")
        
        return orphaned_patients == 0 and orphaned_staff == 0 and users_without_roles == 0
        
    except Exception as e:
        print(f"✗ Database integrity test failed: {str(e)}")
        return False


def test_view_permissions():
    """Test view permission configurations"""
    print("\nTesting View Permissions...")
    print("-" * 60)
    
    try:
        # Check staff management views
        from staff_management.views import StaffProfileViewSet, ShiftViewSet
        
        # Check if StaffProfileViewSet has proper permissions
        staff_permissions = getattr(StaffProfileViewSet, 'permission_classes', [])
        if any('IsAnyAdmin' in str(perm) for perm in staff_permissions):
            print("✓ StaffProfileViewSet has admin permission")
        else:
            print("✗ StaffProfileViewSet missing admin permission")
            return False
        
        # Check if ShiftViewSet has proper permissions
        shift_permissions = getattr(ShiftViewSet, 'permission_classes', [])
        if any('IsAnyAdmin' in str(perm) for perm in shift_permissions):
            print("✓ ShiftViewSet has admin permission")
        else:
            print("✗ ShiftViewSet missing admin permission")
            return False
        
        # Check medical system views
        from medical_system.views import DepartmentViewSet, WardViewSet
        
        dept_permissions = getattr(DepartmentViewSet, 'permission_classes', [])
        if any('IsMedicalStaff' in str(perm) for perm in dept_permissions):
            print("✓ DepartmentViewSet has medical staff permission")
        else:
            print("✗ DepartmentViewSet missing medical staff permission")
            return False
        
        ward_permissions = getattr(WardViewSet, 'permission_classes', [])
        if any('IsMedicalStaff' in str(perm) for perm in ward_permissions):
            print("✓ WardViewSet has medical staff permission")
        else:
            print("✗ WardViewSet missing medical staff permission")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ View permissions test failed: {str(e)}")
        return False


def generate_summary_report(results):
    """Generate summary report"""
    print("\n" + "=" * 80)
    print("HMS SECURITY AUDIT SUMMARY")
    print("=" * 80)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\nDETAILED RESULTS:")
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        print(f"  {test_name.replace('_', ' ').title()}: {status}")
    
    print("-" * 80)
    
    if passed_tests == total_tests:
        print("ALL SECURITY TESTS PASSED!")
        print("✓ HMS application meets enterprise security standards")
        print("✓ Critical security fixes have been successfully implemented")
        print("✓ Permission system is working correctly")
        print("✓ Database integrity is maintained")
        print("✓ Ready for production deployment")
    else:
        print("SECURITY ISSUES DETECTED!")
        print("✗ Some tests failed - review the detailed results above")
        print("✗ Address all issues before deploying to production")
    
    print("=" * 80)
    
    return passed_tests == total_tests


def main():
    """Main execution function"""
    print_header()
    
    # Run all tests
    results = {}
    
    results['basic_imports'] = test_basic_imports()
    results['permission_system'] = test_permission_system()
    results['database_integrity'] = test_database_integrity()
    results['view_permissions'] = test_view_permissions()
    
    # Generate summary
    overall_success = generate_summary_report(results)
    
    # Exit with appropriate code
    sys.exit(0 if overall_success else 1)


if __name__ == "__main__":
    main()