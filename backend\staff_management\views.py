from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Avg
from django.utils import timezone
from datetime import datetime, timedelta

from hms.permissions import IsAnyAdmin, IsHMSAdmin, IsMedicalStaff
from hms.common_utils import PermissionUtils
from .models import (
    StaffProfile, Shift, StaffSchedule, Attendance,
    LeaveRequest, Performance
)
from .serializers import (
    StaffProfileSerializer, ShiftSerializer, StaffScheduleSerializer,
    AttendanceSerializer, LeaveRequestSerializer, PerformanceSerializer
)


class StaffProfileViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing staff profiles
    Only admins can manage staff profiles
    """
    queryset = StaffProfile.objects.all()
    serializer_class = StaffProfileSerializer
    permission_classes = [IsAnyAdmin]  # Only admins can access staff data
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'employment_status', 'position']
    search_fields = ['employee_id', 'user__first_name', 'user__last_name', 'user__email']
    ordering_fields = ['hire_date', 'employee_id', 'created_at']
    ordering = ['-hire_date']

    def get_queryset(self):
        return StaffProfile.objects.select_related('user', 'department')

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get active staff members"""
        queryset = self.get_queryset().filter(employment_status='active')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_department(self, request):
        """Get staff by department"""
        department_id = request.query_params.get('department_id')
        if department_id:
            queryset = self.get_queryset().filter(department_id=department_id)
        else:
            queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def terminate(self, request, pk=None):
        """Terminate staff member - Admin only operation"""
        # Double-check admin permissions for this critical operation
        if not PermissionUtils.has_admin_access(request.user):
            return Response(
                {'error': 'Only administrators can terminate staff members'},
                status=status.HTTP_403_FORBIDDEN
            )

        staff = self.get_object()
        termination_date = request.data.get('termination_date', timezone.now().date())

        staff.employment_status = 'terminated'
        staff.termination_date = termination_date
        staff.save()

        return Response({'status': 'Staff member terminated'})


class ShiftViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing shifts
    Only admins can manage shift configurations
    """
    queryset = Shift.objects.all()
    serializer_class = ShiftSerializer
    permission_classes = [IsAnyAdmin]  # Only admins can manage shifts
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['shift_type', 'is_active']
    search_fields = ['name']
    ordering_fields = ['start_time', 'name', 'created_at']
    ordering = ['start_time']

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get active shifts"""
        queryset = self.get_queryset().filter(is_active=True)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class StaffScheduleViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing staff schedules
    Admins can manage all schedules, medical staff can view relevant schedules
    """
    queryset = StaffSchedule.objects.all()
    serializer_class = StaffScheduleSerializer
    permission_classes = [IsMedicalStaff]  # Medical staff and admins can access schedules
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['staff', 'shift', 'date', 'is_working', 'leave_type']
    search_fields = ['staff__employee_id', 'staff__user__first_name', 'staff__user__last_name']
    ordering_fields = ['date', 'created_at']
    ordering = ['-date']

    def get_queryset(self):
        queryset = StaffSchedule.objects.select_related('staff__user', 'shift')

        # Non-admin users can only view schedules, not modify
        if not PermissionUtils.has_admin_access(self.request.user):
            # Medical staff can view schedules but not modify
            return queryset

        return queryset

    def perform_create(self, serializer):
        # Only admins can create schedules
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can create staff schedules")
        serializer.save()

    def perform_update(self, serializer):
        # Only admins can update schedules
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can update staff schedules")
        serializer.save()

    def perform_destroy(self, instance):
        # Only admins can delete schedules
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can delete staff schedules")
        instance.delete()

    @action(detail=False, methods=['get'])
    def today(self, request):
        """Get today's schedules"""
        today = timezone.now().date()
        queryset = self.get_queryset().filter(date=today)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def week(self, request):
        """Get this week's schedules"""
        today = timezone.now().date()
        start_week = today - timedelta(days=today.weekday())
        end_week = start_week + timedelta(days=6)

        queryset = self.get_queryset().filter(date__range=[start_week, end_week])
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class AttendanceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing attendance
    Admins can manage all attendance, medical staff can view relevant attendance
    """
    queryset = Attendance.objects.all()
    serializer_class = AttendanceSerializer
    permission_classes = [IsMedicalStaff]  # Medical staff and admins can access attendance
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['staff', 'date', 'status']
    search_fields = ['staff__employee_id', 'staff__user__first_name', 'staff__user__last_name']
    ordering_fields = ['date', 'clock_in', 'created_at']
    ordering = ['-date']

    def get_queryset(self):
        queryset = Attendance.objects.select_related('staff__user')

        # Non-admin users have limited access
        if not PermissionUtils.has_admin_access(self.request.user):
            # Medical staff can view attendance but with restrictions
            return queryset

        return queryset

    @action(detail=False, methods=['get'])
    def today(self, request):
        """Get today's attendance"""
        today = timezone.now().date()
        queryset = self.get_queryset().filter(date=today)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def clock_in(self, request, pk=None):
        """Clock in staff member"""
        attendance = self.get_object()
        attendance.clock_in = timezone.now()
        attendance.status = 'present'
        attendance.save()
        return Response({'status': 'Clocked in successfully'})

    @action(detail=True, methods=['post'])
    def clock_out(self, request, pk=None):
        """Clock out staff member"""
        attendance = self.get_object()
        attendance.clock_out = timezone.now()
        attendance.save()
        return Response({'status': 'Clocked out successfully'})


class LeaveRequestViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing leave requests
    Staff can view their own requests, admins can manage all requests
    """
    queryset = LeaveRequest.objects.all()
    serializer_class = LeaveRequestSerializer
    permission_classes = [IsAuthenticated]  # All authenticated users can access
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['staff', 'leave_type', 'status']
    search_fields = ['staff__employee_id', 'staff__user__first_name', 'staff__user__last_name']
    ordering_fields = ['start_date', 'created_at']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = LeaveRequest.objects.select_related('staff__user', 'approved_by')

        # Non-admin users can only see their own leave requests
        if not PermissionUtils.has_admin_access(self.request.user):
            # Filter to only show requests for the current user's staff profile
            queryset = queryset.filter(staff__user=self.request.user)

        return queryset

    @action(detail=False, methods=['get'])
    def pending(self, request):
        """Get pending leave requests"""
        queryset = self.get_queryset().filter(status='pending')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve leave request - Admin only operation"""
        if not PermissionUtils.has_admin_access(request.user):
            return Response(
                {'error': 'Only administrators can approve leave requests'},
                status=status.HTTP_403_FORBIDDEN
            )

        leave_request = self.get_object()
        leave_request.status = 'approved'
        leave_request.approved_by = request.user
        leave_request.approval_date = timezone.now()
        leave_request.save()
        return Response({'status': 'Leave request approved'})

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject leave request - Admin only operation"""
        if not PermissionUtils.has_admin_access(request.user):
            return Response(
                {'error': 'Only administrators can reject leave requests'},
                status=status.HTTP_403_FORBIDDEN
            )

        leave_request = self.get_object()
        rejection_reason = request.data.get('rejection_reason', '')

        leave_request.status = 'rejected'
        leave_request.approved_by = request.user
        leave_request.approval_date = timezone.now()
        leave_request.rejection_reason = rejection_reason
        leave_request.save()
        return Response({'status': 'Leave request rejected'})


class PerformanceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing performance evaluations
    Staff can view their own evaluations, admins can manage all evaluations
    """
    queryset = Performance.objects.all()
    serializer_class = PerformanceSerializer
    permission_classes = [IsAuthenticated]  # All authenticated users can access
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['staff', 'evaluator', 'status']
    search_fields = ['staff__employee_id', 'staff__user__first_name', 'staff__user__last_name']
    ordering_fields = ['evaluation_period_end', 'overall_rating', 'created_at']
    ordering = ['-evaluation_period_end']

    def get_queryset(self):
        queryset = Performance.objects.select_related('staff__user', 'evaluator')

        # Non-admin users can only see their own performance evaluations
        if not PermissionUtils.has_admin_access(self.request.user):
            queryset = queryset.filter(staff__user=self.request.user)

        return queryset

    def perform_create(self, serializer):
        # Only admins can create performance evaluations
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can create performance evaluations")
        serializer.save()

    def perform_update(self, serializer):
        # Only admins can update performance evaluations (except acknowledgment)
        if not PermissionUtils.has_admin_access(self.request.user):
            # Allow staff to acknowledge their own evaluations
            instance = self.get_object()
            if instance.staff.user != self.request.user:
                raise PermissionError("You can only acknowledge your own performance evaluations")
        serializer.save()

    def perform_destroy(self, instance):
        # Only admins can delete performance evaluations
        if not PermissionUtils.has_admin_access(self.request.user):
            raise PermissionError("Only administrators can delete performance evaluations")
        instance.delete()

    @action(detail=True, methods=['post'])
    def acknowledge(self, request, pk=None):
        """Staff acknowledges performance evaluation"""
        performance = self.get_object()
        staff_comments = request.data.get('staff_comments', '')

        performance.status = 'acknowledged'
        performance.staff_acknowledgment_date = timezone.now()
        performance.staff_comments = staff_comments
        performance.save()
        return Response({'status': 'Performance evaluation acknowledged'})
