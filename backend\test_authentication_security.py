"""
Comprehensive Authentication and Authorization Security Testing for HMS
Tests JWT token management, permission enforcement, and security vulnerabilities
"""

import os
import sys
import django
import json
from datetime import datetime, timedelta
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()

from users.models import User
from patient_management.models import Patient
from staff_management.models import StaffProfile, LeaveRequest
from hms.common_utils import PermissionUtils


class AuthenticationSecurityTest(APITestCase):
    """
    Comprehensive authentication and authorization security tests
    """
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create test users
        self.admin_user = User.objects.create_user(
            username='admin_security',
            email='<EMAIL>',
            password='admin123',
            role=User.Role.ADMIN,
            first_name='Admin',
            last_name='Security'
        )
        
        self.doctor_user = User.objects.create_user(
            username='doctor_security',
            email='<EMAIL>',
            password='doctor123',
            role=User.Role.DOCTOR,
            first_name='Doctor',
            last_name='Security'
        )
        
        self.patient_user = User.objects.create_user(
            username='patient_security',
            email='<EMAIL>',
            password='patient123',
            role=User.Role.PATIENT,
            first_name='Patient',
            last_name='Security'
        )
        
        # Create patient profile
        self.patient_profile = Patient.objects.create(
            user=self.patient_user,
            patient_id='P000999',
            blood_group='A+',
            insurance_provider='Security Insurance'
        )
        
        # Create staff profile
        self.admin_staff = StaffProfile.objects.create(
            user=self.admin_user,
            employee_id='EMP999',
            position='Security Admin',
            employment_status='active'
        )
        
        # Create leave request for testing
        self.leave_request = LeaveRequest.objects.create(
            staff=self.admin_staff,
            leave_type='annual',
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=5),
            reason='Security testing',
            status='pending'
        )
    
    def get_jwt_token(self, user):
        """Get JWT token for user"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def test_login_authentication(self):
        """Test login authentication process"""
        login_url = '/api/auth/login/'
        
        # Test valid login
        response = self.client.post(login_url, {
            'username': 'admin_security',
            'password': 'admin123'
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('tokens', response.data)
        self.assertIn('access', response.data['tokens'])
        self.assertIn('refresh', response.data['tokens'])
        self.assertIn('user', response.data)
        
        # Test invalid credentials
        response = self.client.post(login_url, {
            'username': 'admin_security',
            'password': 'wrong_password'
        })
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Test missing credentials
        response = self.client.post(login_url, {})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_token_authentication(self):
        """Test JWT token authentication"""
        # Get valid token
        token = self.get_jwt_token(self.admin_user)
        
        # Test with valid token
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.get('/api/auth/profile/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Test with invalid token
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid_token')
        response = self.client.get('/api/auth/profile/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Test without token
        self.client.credentials()
        response = self.client.get('/api/auth/profile/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_permission_enforcement(self):
        """Test permission enforcement across different endpoints"""
        
        # Test admin-only endpoints
        admin_endpoints = [
            '/api/staff/staff-profiles/',
            '/api/staff/shifts/',
        ]
        
        for endpoint in admin_endpoints:
            # Test with admin user (should work)
            token = self.get_jwt_token(self.admin_user)
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            response = self.client.get(endpoint)
            self.assertEqual(response.status_code, status.HTTP_200_OK,
                           f"Admin should have access to {endpoint}")
            
            # Test with non-admin user (should fail)
            token = self.get_jwt_token(self.patient_user)
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            response = self.client.get(endpoint)
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN,
                           f"Patient should not have access to {endpoint}")
    
    def test_object_level_permissions(self):
        """Test object-level permission enforcement"""
        
        # Test leave request access
        leave_url = f'/api/staff/leave-requests/{self.leave_request.id}/'
        
        # Admin should have access
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.get(leave_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK,
                        "Admin should have access to leave request")
        
        # Patient should not have access
        token = self.get_jwt_token(self.patient_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.get(leave_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND,
                        "Patient should not have access to leave request")
    
    def test_privilege_escalation_prevention(self):
        """Test prevention of privilege escalation attacks"""
        
        # Test staff termination by non-admin
        terminate_url = f'/api/staff/staff-profiles/{self.admin_staff.id}/terminate/'
        
        token = self.get_jwt_token(self.doctor_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.post(terminate_url, {'termination_date': '2024-01-15'})
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN,
                        "Doctor should not be able to terminate staff")
        
        # Test leave request approval by non-admin
        approve_url = f'/api/staff/leave-requests/{self.leave_request.id}/approve/'
        
        response = self.client.post(approve_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN,
                        "Doctor should not be able to approve leave requests")
    
    def test_data_isolation(self):
        """Test data isolation between different user roles"""
        
        # Create another patient
        other_patient_user = User.objects.create_user(
            username='other_patient',
            email='<EMAIL>',
            password='patient123',
            role=User.Role.PATIENT
        )
        
        other_patient_profile = Patient.objects.create(
            user=other_patient_user,
            patient_id='P000998',
            blood_group='B+',
            insurance_provider='Other Insurance'
        )
        
        # Test that patient can only see their own data
        token = self.get_jwt_token(self.patient_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.get('/api/patients/patients/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        patient_ids = [p['patient_id'] for p in response.data['results']]
        self.assertIn('P000999', patient_ids, "Patient should see their own data")
        self.assertNotIn('P000998', patient_ids, "Patient should not see other patient's data")
    
    def test_user_registration_security(self):
        """Test user registration security"""
        register_url = '/api/auth/register/'
        
        # Test registration without authentication (should fail)
        response = self.client.post(register_url, {
            'username': 'new_user',
            'email': '<EMAIL>',
            'password': 'newuser123',
            'role': 'doctor'
        })
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED,
                        "Registration should require authentication")
        
        # Test registration with non-admin user (should fail)
        token = self.get_jwt_token(self.patient_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.post(register_url, {
            'username': 'new_user',
            'email': '<EMAIL>',
            'password': 'newuser123',
            'role': 'doctor'
        })
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN,
                        "Only admins should be able to register users")
        
        # Test registration with admin user (should work)
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.post(register_url, {
            'username': 'new_user',
            'email': '<EMAIL>',
            'password': 'newuser123',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'doctor'
        })
        self.assertEqual(response.status_code, status.HTTP_201_CREATED,
                        "Admin should be able to register users")
    
    def test_permission_utils_consistency(self):
        """Test PermissionUtils consistency with actual permissions"""
        
        # Test admin access detection
        self.assertTrue(PermissionUtils.has_admin_access(self.admin_user),
                       "PermissionUtils should detect admin access")
        self.assertFalse(PermissionUtils.has_admin_access(self.patient_user),
                        "PermissionUtils should not detect admin access for patient")
        
        # Test HMS admin access
        self.assertTrue(PermissionUtils.has_hms_admin_access(self.admin_user),
                       "PermissionUtils should detect HMS admin access")
        self.assertFalse(PermissionUtils.has_hms_admin_access(self.doctor_user),
                        "PermissionUtils should not detect HMS admin access for doctor")
        
        # Test user creation permissions
        self.assertTrue(PermissionUtils.user_can_create_users(self.admin_user),
                       "Admin should be able to create users")
        self.assertFalse(PermissionUtils.user_can_create_users(self.patient_user),
                        "Patient should not be able to create users")


def run_authentication_security_tests():
    """Run all authentication and security tests"""
    import unittest
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(AuthenticationSecurityTest)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print("AUTHENTICATION & SECURITY TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print(f"\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print(f"\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    if result.wasSuccessful():
        print(f"\n✅ ALL AUTHENTICATION & SECURITY TESTS PASSED!")
        return True
    else:
        print(f"\n❌ SOME SECURITY TESTS FAILED - VULNERABILITIES DETECTED!")
        return False


if __name__ == '__main__':
    success = run_authentication_security_tests()
    sys.exit(0 if success else 1)
