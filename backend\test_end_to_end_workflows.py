"""
End-to-End Workflow Testing for HMS
Tests complete user workflows across all roles and critical business processes
"""

import os
import sys
import django
import json
from datetime import datetime, timedelta
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()

from users.models import User
from patient_management.models import Patient, MedicalRecord, Prescription
from staff_management.models import StaffProfile, LeaveRequest
from appointment_system.models import Appointment
from medical_system.models import Department, Ward, Bed, Admission


class EndToEndWorkflowTest(APITestCase):
    """
    End-to-end workflow testing for all user roles
    """
    
    def setUp(self):
        """Set up comprehensive test environment"""
        self.client = APIClient()
        
        # Create all user types
        self.admin_user = User.objects.create_user(
            username='admin_e2e',
            email='<EMAIL>',
            password='admin123',
            role=User.Role.ADMIN,
            first_name='Admin',
            last_name='E2E'
        )
        
        self.doctor_user = User.objects.create_user(
            username='doctor_e2e',
            email='<EMAIL>',
            password='doctor123',
            role=User.Role.DOCTOR,
            first_name='Doctor',
            last_name='E2E'
        )
        
        self.nurse_user = User.objects.create_user(
            username='nurse_e2e',
            email='<EMAIL>',
            password='nurse123',
            role=User.Role.NURSE,
            first_name='Nurse',
            last_name='E2E'
        )
        
        self.receptionist_user = User.objects.create_user(
            username='receptionist_e2e',
            email='<EMAIL>',
            password='receptionist123',
            role=User.Role.RECEPTIONIST,
            first_name='Receptionist',
            last_name='E2E'
        )
        
        self.patient_user = User.objects.create_user(
            username='patient_e2e',
            email='<EMAIL>',
            password='patient123',
            role=User.Role.PATIENT,
            first_name='Patient',
            last_name='E2E'
        )
        
        # Create medical infrastructure
        self.department = Department.objects.create(
            name='Emergency Department',
            description='Emergency medical services',
            location='Ground Floor',
            head_of_department=self.doctor_user
        )
        
        self.ward = Ward.objects.create(
            name='Emergency Ward',
            ward_number='EW001',
            department=self.department,
            ward_type='emergency',
            capacity=10
        )
        
        self.bed = Bed.objects.create(
            bed_number='EB001',
            ward=self.ward,
            bed_type='emergency',
            status='available'
        )
        
        # Create staff profiles
        self.admin_staff = StaffProfile.objects.create(
            user=self.admin_user,
            employee_id='ADMIN001',
            position='Administrator',
            employment_status='active'
        )
        
        self.doctor_staff = StaffProfile.objects.create(
            user=self.doctor_user,
            employee_id='DOC001',
            position='Emergency Doctor',
            employment_status='active'
        )
    
    def get_jwt_token(self, user):
        """Get JWT token for user"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def authenticate_user(self, user):
        """Authenticate user for API requests"""
        token = self.get_jwt_token(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
    
    def test_complete_patient_registration_workflow(self):
        """Test complete patient registration workflow"""
        print("\n🏥 Testing Patient Registration Workflow...")
        
        # Step 1: Admin registers a new patient
        self.authenticate_user(self.admin_user)
        
        patient_data = {
            'username': 'new_patient_e2e',
            'email': '<EMAIL>',
            'password': 'patient123',
            'first_name': 'New',
            'last_name': 'Patient',
            'role': 'patient',
            'phone_number': '+**********',
            'date_of_birth': '1990-01-01',
            'address': '123 Test Street',
            'blood_group': 'O+',
            'insurance_provider': 'Test Insurance'
        }
        
        response = self.client.post('/api/auth/register/', patient_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED,
                        "Admin should be able to register new patient")
        
        new_patient_id = response.data['user']['id']
        
        # Step 2: Verify patient can login
        login_response = self.client.post('/api/auth/login/', {
            'username': 'new_patient_e2e',
            'password': 'patient123'
        })
        self.assertEqual(login_response.status_code, status.HTTP_200_OK,
                        "New patient should be able to login")
        
        # Step 3: Verify patient profile was created
        self.authenticate_user(self.admin_user)
        patients_response = self.client.get('/api/patients/patients/')
        self.assertEqual(patients_response.status_code, status.HTTP_200_OK)
        
        patient_usernames = [p['user']['username'] for p in patients_response.data['results']]
        self.assertIn('new_patient_e2e', patient_usernames,
                     "New patient should appear in patient list")
        
        print("✅ Patient Registration Workflow: PASSED")
    
    def test_complete_appointment_workflow(self):
        """Test complete appointment booking and management workflow"""
        print("\n📅 Testing Appointment Workflow...")
        
        # Step 1: Create patient profile
        patient_profile = Patient.objects.create(
            user=self.patient_user,
            patient_id='P000E2E',
            blood_group='A+',
            insurance_provider='E2E Insurance'
        )
        
        # Step 2: Receptionist creates appointment
        self.authenticate_user(self.receptionist_user)
        
        appointment_data = {
            'patient': patient_profile.id,
            'doctor': self.doctor_user.id,
            'appointment_date': (timezone.now().date() + timedelta(days=1)).isoformat(),
            'appointment_time': '10:00:00',
            'appointment_type': 'consultation',
            'reason': 'Regular checkup',
            'status': 'scheduled'
        }
        
        response = self.client.post('/api/appointments/appointments/', appointment_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED,
                        "Receptionist should be able to create appointments")
        
        appointment_id = response.data['id']
        
        # Step 3: Doctor confirms appointment
        self.authenticate_user(self.doctor_user)
        
        confirm_response = self.client.post(f'/api/appointments/appointments/{appointment_id}/confirm/')
        self.assertEqual(confirm_response.status_code, status.HTTP_200_OK,
                        "Doctor should be able to confirm appointments")
        
        # Step 4: Patient views their appointments
        self.authenticate_user(self.patient_user)
        
        patient_appointments = self.client.get('/api/appointments/appointments/')
        self.assertEqual(patient_appointments.status_code, status.HTTP_200_OK)
        
        appointment_ids = [a['id'] for a in patient_appointments.data['results']]
        self.assertIn(appointment_id, appointment_ids,
                     "Patient should see their appointment")
        
        print("✅ Appointment Workflow: PASSED")
    
    def test_complete_medical_record_workflow(self):
        """Test complete medical record creation and access workflow"""
        print("\n📋 Testing Medical Record Workflow...")
        
        # Step 1: Create patient profile
        patient_profile = Patient.objects.create(
            user=self.patient_user,
            patient_id='P000MED',
            blood_group='B+',
            insurance_provider='Medical Insurance'
        )
        
        # Step 2: Doctor creates medical record
        self.authenticate_user(self.doctor_user)
        
        medical_record_data = {
            'patient': patient_profile.id,
            'doctor': self.doctor_user.id,
            'record_type': 'consultation',
            'chief_complaint': 'Headache and fever',
            'history_of_present_illness': 'Patient reports headache for 2 days',
            'physical_examination': 'Temperature 38.5°C, BP 120/80',
            'diagnosis': 'Viral fever',
            'treatment_plan': 'Rest and paracetamol',
            'follow_up_date': (timezone.now().date() + timedelta(days=7)).isoformat()
        }
        
        response = self.client.post('/api/patients/medical-records/', medical_record_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED,
                        "Doctor should be able to create medical records")
        
        record_id = response.data['id']
        
        # Step 3: Patient views their medical records
        self.authenticate_user(self.patient_user)
        
        patient_records = self.client.get('/api/patients/medical-records/')
        self.assertEqual(patient_records.status_code, status.HTTP_200_OK)
        
        record_ids = [r['id'] for r in patient_records.data['results']]
        self.assertIn(record_id, record_ids,
                     "Patient should see their medical record")
        
        # Step 4: Nurse can view medical records
        self.authenticate_user(self.nurse_user)
        
        nurse_records = self.client.get('/api/patients/medical-records/')
        self.assertEqual(nurse_records.status_code, status.HTTP_200_OK,
                        "Nurse should be able to view medical records")
        
        print("✅ Medical Record Workflow: PASSED")
    
    def test_complete_staff_management_workflow(self):
        """Test complete staff management workflow"""
        print("\n👥 Testing Staff Management Workflow...")
        
        # Step 1: Admin creates leave request for staff
        self.authenticate_user(self.admin_user)
        
        leave_request_data = {
            'staff': self.doctor_staff.id,
            'leave_type': 'annual',
            'start_date': (timezone.now().date() + timedelta(days=10)).isoformat(),
            'end_date': (timezone.now().date() + timedelta(days=15)).isoformat(),
            'reason': 'Annual vacation',
            'status': 'pending'
        }
        
        response = self.client.post('/api/staff/leave-requests/', leave_request_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED,
                        "Admin should be able to create leave requests")
        
        leave_request_id = response.data['id']
        
        # Step 2: Admin approves leave request
        approve_response = self.client.post(f'/api/staff/leave-requests/{leave_request_id}/approve/')
        self.assertEqual(approve_response.status_code, status.HTTP_200_OK,
                        "Admin should be able to approve leave requests")
        
        # Step 3: Verify leave request status
        leave_detail = self.client.get(f'/api/staff/leave-requests/{leave_request_id}/')
        self.assertEqual(leave_detail.status_code, status.HTTP_200_OK)
        self.assertEqual(leave_detail.data['status'], 'approved',
                        "Leave request should be approved")
        
        # Step 4: Doctor views their own leave requests
        self.authenticate_user(self.doctor_user)
        
        doctor_leaves = self.client.get('/api/staff/leave-requests/')
        self.assertEqual(doctor_leaves.status_code, status.HTTP_200_OK)
        
        doctor_leave_ids = [l['id'] for l in doctor_leaves.data['results']]
        self.assertIn(leave_request_id, doctor_leave_ids,
                     "Doctor should see their own leave request")
        
        print("✅ Staff Management Workflow: PASSED")
    
    def test_complete_emergency_admission_workflow(self):
        """Test complete emergency admission workflow"""
        print("\n🚨 Testing Emergency Admission Workflow...")
        
        # Step 1: Create patient profile
        patient_profile = Patient.objects.create(
            user=self.patient_user,
            patient_id='P000EMR',
            blood_group='AB+',
            insurance_provider='Emergency Insurance'
        )
        
        # Step 2: Doctor admits patient
        self.authenticate_user(self.doctor_user)
        
        admission_data = {
            'patient': patient_profile.id,
            'bed': self.bed.id,
            'admitting_doctor': self.doctor_user.id,
            'admission_date': timezone.now().date().isoformat(),
            'admission_reason': 'Emergency treatment required',
            'status': 'admitted'
        }
        
        response = self.client.post('/api/medical/admissions/', admission_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED,
                        "Doctor should be able to admit patients")
        
        admission_id = response.data['id']
        
        # Step 3: Verify bed is occupied
        bed_detail = self.client.get(f'/api/medical/beds/{self.bed.id}/')
        self.assertEqual(bed_detail.status_code, status.HTTP_200_OK)
        
        # Step 4: Patient can view their admission
        self.authenticate_user(self.patient_user)
        
        patient_admissions = self.client.get('/api/medical/admissions/')
        self.assertEqual(patient_admissions.status_code, status.HTTP_200_OK)
        
        admission_ids = [a['id'] for a in patient_admissions.data['results']]
        self.assertIn(admission_id, admission_ids,
                     "Patient should see their admission")
        
        print("✅ Emergency Admission Workflow: PASSED")


def run_end_to_end_tests():
    """Run all end-to-end workflow tests"""
    import unittest
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(EndToEndWorkflowTest)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print("END-TO-END WORKFLOW TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print(f"\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print(f"\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    if result.wasSuccessful():
        print(f"\n✅ ALL END-TO-END WORKFLOW TESTS PASSED!")
        return True
    else:
        print(f"\n❌ SOME WORKFLOW TESTS FAILED!")
        return False


if __name__ == '__main__':
    success = run_end_to_end_tests()
    sys.exit(0 if success else 1)
