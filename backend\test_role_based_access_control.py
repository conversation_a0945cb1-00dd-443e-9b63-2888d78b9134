"""
Comprehensive Role-Based Access Control Testing for HMS
Tests all user roles and their access permissions across the system
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()

from users.models import User
from patient_management.models import Patient
from staff_management.models import StaffProfile
from appointment_system.models import Appointment
from medical_system.models import Department, Ward, Bed


class RoleBasedAccessControlTest(APITestCase):
    """
    Comprehensive test suite for role-based access control
    Tests all user roles against all protected endpoints
    """
    
    def setUp(self):
        """Set up test data for all user roles"""
        self.client = APIClient()
        
        # Create users for each role
        self.admin_user = User.objects.create_user(
            username='admin_test',
            email='<EMAIL>',
            password='admin123',
            role=User.Role.ADMIN,
            first_name='Admin',
            last_name='User'
        )
        
        self.doctor_user = User.objects.create_user(
            username='doctor_test',
            email='<EMAIL>',
            password='doctor123',
            role=User.Role.DOCTOR,
            first_name='Doctor',
            last_name='User'
        )
        
        self.nurse_user = User.objects.create_user(
            username='nurse_test',
            email='<EMAIL>',
            password='nurse123',
            role=User.Role.NURSE,
            first_name='Nurse',
            last_name='User'
        )
        
        self.receptionist_user = User.objects.create_user(
            username='receptionist_test',
            email='<EMAIL>',
            password='receptionist123',
            role=User.Role.RECEPTIONIST,
            first_name='Receptionist',
            last_name='User'
        )
        
        self.patient_user = User.objects.create_user(
            username='patient_test',
            email='<EMAIL>',
            password='patient123',
            role=User.Role.PATIENT,
            first_name='Patient',
            last_name='User'
        )
        
        # Create a Django superuser
        self.superuser = User.objects.create_superuser(
            username='superuser_test',
            email='<EMAIL>',
            password='super123',
            role=User.Role.PATIENT,  # Role doesn't matter for superuser
            first_name='Super',
            last_name='User'
        )
        
        # Create test data
        self.create_test_data()
    
    def create_test_data(self):
        """Create test data for testing"""
        # Create patient profile
        self.patient_profile = Patient.objects.create(
            user=self.patient_user,
            patient_id='P000001',
            blood_group='O+',
            insurance_provider='Test Insurance'
        )
        
        # Create staff profiles
        self.admin_staff = StaffProfile.objects.create(
            user=self.admin_user,
            employee_id='EMP001',
            position='Administrator',
            employment_status='active'
        )
        
        self.doctor_staff = StaffProfile.objects.create(
            user=self.doctor_user,
            employee_id='EMP002',
            position='Doctor',
            employment_status='active'
        )
        
        # Create medical system data
        self.department = Department.objects.create(
            name='Test Department',
            description='Test Department Description',
            location='Test Location',
            head_of_department=self.doctor_user
        )
        
        self.ward = Ward.objects.create(
            name='Test Ward',
            ward_number='W001',
            department=self.department,
            ward_type='general',
            capacity=20
        )
        
        self.bed = Bed.objects.create(
            bed_number='B001',
            ward=self.ward,
            bed_type='standard',
            status='available'
        )
    
    def get_jwt_token(self, user):
        """Get JWT token for user authentication"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def authenticate_user(self, user):
        """Authenticate user for API requests"""
        token = self.get_jwt_token(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
    
    def test_staff_profile_access_control(self):
        """Test staff profile access control for all roles"""
        url = '/api/staff/staff-profiles/'
        
        # Test admin access (should work)
        self.authenticate_user(self.admin_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK, 
                        "Admin should have access to staff profiles")
        
        # Test superuser access (should work)
        self.authenticate_user(self.superuser)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK, 
                        "Superuser should have access to staff profiles")
        
        # Test doctor access (should be forbidden)
        self.authenticate_user(self.doctor_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN, 
                        "Doctor should not have access to staff profiles")
        
        # Test nurse access (should be forbidden)
        self.authenticate_user(self.nurse_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN, 
                        "Nurse should not have access to staff profiles")
        
        # Test receptionist access (should be forbidden)
        self.authenticate_user(self.receptionist_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN, 
                        "Receptionist should not have access to staff profiles")
        
        # Test patient access (should be forbidden)
        self.authenticate_user(self.patient_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN, 
                        "Patient should not have access to staff profiles")
    
    def test_staff_termination_access_control(self):
        """Test staff termination access control"""
        url = f'/api/staff/staff-profiles/{self.doctor_staff.id}/terminate/'
        
        # Test admin access (should work)
        self.authenticate_user(self.admin_user)
        response = self.client.post(url, {'termination_date': '2024-01-15'})
        self.assertEqual(response.status_code, status.HTTP_200_OK, 
                        "Admin should be able to terminate staff")
        
        # Reset staff status
        self.doctor_staff.employment_status = 'active'
        self.doctor_staff.save()
        
        # Test doctor access (should be forbidden)
        self.authenticate_user(self.doctor_user)
        response = self.client.post(url, {'termination_date': '2024-01-15'})
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN, 
                        "Doctor should not be able to terminate staff")
    
    def test_medical_system_access_control(self):
        """Test medical system access control"""
        department_url = '/api/medical/departments/'
        ward_url = '/api/medical/wards/'
        bed_url = '/api/medical/beds/'
        
        # Test medical staff access (should work for viewing)
        for user in [self.doctor_user, self.nurse_user]:
            self.authenticate_user(user)
            
            response = self.client.get(department_url)
            self.assertEqual(response.status_code, status.HTTP_200_OK, 
                            f"{user.role} should have access to view departments")
            
            response = self.client.get(ward_url)
            self.assertEqual(response.status_code, status.HTTP_200_OK, 
                            f"{user.role} should have access to view wards")
            
            response = self.client.get(bed_url)
            self.assertEqual(response.status_code, status.HTTP_200_OK, 
                            f"{user.role} should have access to view beds")
        
        # Test patient access (should be forbidden)
        self.authenticate_user(self.patient_user)
        
        response = self.client.get(department_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN, 
                        "Patient should not have access to departments")
        
        response = self.client.get(ward_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN, 
                        "Patient should not have access to wards")
        
        response = self.client.get(bed_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN, 
                        "Patient should not have access to beds")
    
    def test_patient_data_access_control(self):
        """Test patient data access control"""
        url = '/api/patients/patients/'
        
        # Test admin access (should see all patients)
        self.authenticate_user(self.admin_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data['results']), 1, 
                               "Admin should see all patients")
        
        # Test patient access (should only see own data)
        self.authenticate_user(self.patient_user)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Patient should only see their own record
        patient_ids = [p['patient_id'] for p in response.data['results']]
        self.assertIn('P000001', patient_ids, 
                     "Patient should see their own record")
        
        # Test medical staff access
        for user in [self.doctor_user, self.nurse_user, self.receptionist_user]:
            self.authenticate_user(user)
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_200_OK, 
                            f"{user.role} should have access to patient data")


def run_role_based_tests():
    """Run all role-based access control tests"""
    import unittest
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(RoleBasedAccessControlTest)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print("ROLE-BASED ACCESS CONTROL TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print(f"\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print(f"\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    if result.wasSuccessful():
        print(f"\n✅ ALL ROLE-BASED ACCESS CONTROL TESTS PASSED!")
        return True
    else:
        print(f"\n❌ SOME TESTS FAILED - SECURITY ISSUES DETECTED!")
        return False


if __name__ == '__main__':
    success = run_role_based_tests()
    sys.exit(0 if success else 1)
