from django.contrib.auth.models import AbstractUser
from django.db import models


class User(AbstractUser):
    """
    Custom User model with role-based access control for HMS
    """

    class Role(models.TextChoices):
        ADMIN = 'admin', 'Admin'
        DOCTOR = 'doctor', 'Doctor'
        NURSE = 'nurse', 'Nurse'
        PATIENT = 'patient', 'Patient'
        RECEPTIONIST = 'receptionist', 'Receptionist'

    role = models.CharField(
        max_length=20,
        choices=Role.choices,
        default=Role.PATIENT
    )

    # Additional fields
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    emergency_contact = models.CharField(max_length=15, blank=True, null=True)

    # Profile fields
    profile_picture = models.ImageField(upload_to='profile_pics/', blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTime<PERSON>ield(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def is_admin(self):
        """
        Check if user is HMS admin (business logic admin)
        This is separate from Django's is_superuser (technical admin)
        """
        return self.role == self.Role.ADMIN

    @property
    def is_hms_admin(self):
        """
        Alias for is_admin for clarity - HMS business admin
        """
        return self.is_admin

    @property
    def is_technical_admin(self):
        """
        Check if user is Django technical admin (superuser)
        Used for Django admin interface access
        """
        return self.is_superuser

    @property
    def has_admin_privileges(self):
        """
        Check if user has any admin privileges (HMS admin OR Django superuser)
        Use this for general admin permission checks
        """
        return self.is_admin or self.is_superuser

    @property
    def is_doctor(self):
        return self.role == self.Role.DOCTOR

    @property
    def is_nurse(self):
        return self.role == self.Role.NURSE

    @property
    def is_patient(self):
        return self.role == self.Role.PATIENT

    @property
    def is_receptionist(self):
        return self.role == self.Role.RECEPTIONIST

    class Meta:
        db_table = 'users'
        verbose_name = 'User'
        verbose_name_plural = 'Users'


class DoctorProfile(models.Model):
    """
    Doctor-specific profile information
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='doctor_profile')
    specialization = models.CharField(max_length=100)
    license_number = models.CharField(max_length=50, unique=True)
    years_of_experience = models.PositiveIntegerField()
    consultation_fee = models.DecimalField(max_digits=10, decimal_places=5)
    available_days = models.CharField(max_length=100)
    available_time_start = models.TimeField()
    available_time_end = models.TimeField()

    def __str__(self):
        return f"Dr. {self.user.full_name} - {self.specialization}"

    class Meta:
        db_table = 'doctor_profiles'
        verbose_name = 'Doctor Profile'
        verbose_name_plural = 'Doctor Profiles'


class NurseProfile(models.Model):
    """
    Nurse-specific profile information
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='nurse_profile')
    license_number = models.CharField(max_length=50, unique=True)
    department = models.CharField(max_length=100)
    shift = models.CharField(max_length=20, choices=[
        ('day', 'Day Shift'),
        ('night', 'Night Shift'),
        ('rotating', 'Rotating Shift'),
    ])

    def __str__(self):
        return f"Nurse {self.user.full_name} - {self.department}"

    class Meta:
        db_table = 'nurse_profiles'
        verbose_name = 'Nurse Profile'
        verbose_name_plural = 'Nurse Profiles'



