from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON><PERSON>icated, IsAdminUser
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from django.contrib.auth import get_user_model

from .models import DoctorProfile, NurseProfile
from .serializers import (
    UserSerializer, UserCreateSerializer, UserUpdateSerializer,
    UserDetailSerializer, DoctorProfileSerializer, NurseProfileSerializer
)

User = get_user_model()


class UserViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing users
    """
    queryset = User.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['role', 'is_active']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    ordering_fields = ['date_joined', 'username', 'last_login']
    ordering = ['-date_joined']

    def get_serializer_class(self):
        if self.action == 'create':
            return UserCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        elif self.action == 'retrieve':
            return UserDetailSerializer
        return UserSerializer

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ['create', 'destroy']:
            permission_classes = [IsAdminUser]
        elif self.action in ['update', 'partial_update']:
            permission_classes = [IsAuthenticated]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        from hms.common_utils import PermissionUtils
        queryset = User.objects.all()

        # Non-admin users can only see active users
        if not PermissionUtils.has_admin_access(self.request.user):
            queryset = queryset.filter(is_active=True)

        return queryset

    @action(detail=False, methods=['get'])
    def doctors(self, request):
        """Get all doctors"""
        queryset = self.get_queryset().filter(role='doctor')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def nurses(self, request):
        """Get all nurses"""
        queryset = self.get_queryset().filter(role='nurse')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def patients(self, request):
        """Get all patients"""
        queryset = self.get_queryset().filter(role='patient')
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def staff(self, request):
        """Get all staff members (doctors, nurses, receptionists)"""
        queryset = self.get_queryset().filter(
            role__in=['doctor', 'nurse', 'receptionist']
        )
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate user account"""
        user = self.get_object()
        user.is_active = True
        user.save()
        return Response({'status': 'User activated'})

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate user account"""
        user = self.get_object()
        user.is_active = False
        user.save()
        return Response({'status': 'User deactivated'})

    @action(detail=True, methods=['post'])
    def reset_password(self, request, pk=None):
        """Reset user password"""
        user = self.get_object()
        new_password = request.data.get('new_password')

        if not new_password:
            return Response(
                {'error': 'New password is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        user.set_password(new_password)
        user.save()
        return Response({'status': 'Password reset successfully'})


class DoctorProfileViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing doctor profiles
    """
    queryset = DoctorProfile.objects.all()
    serializer_class = DoctorProfileSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['specialization', 'department']
    search_fields = ['user__first_name', 'user__last_name', 'license_number', 'specialization']
    ordering_fields = ['user__first_name', 'years_of_experience', 'created_at']
    ordering = ['user__first_name']

    def get_queryset(self):
        return DoctorProfile.objects.select_related('user')

    @action(detail=False, methods=['get'])
    def available(self, request):
        """Get available doctors"""
        day = request.query_params.get('day')
        queryset = self.get_queryset().filter(user__is_active=True)

        if day:
            queryset = queryset.filter(available_days__icontains=day)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_specialization(self, request):
        """Get doctors by specialization"""
        specialization = request.query_params.get('specialization')
        if specialization:
            queryset = self.get_queryset().filter(specialization__icontains=specialization)
        else:
            queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class NurseProfileViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing nurse profiles
    """
    queryset = NurseProfile.objects.all()
    serializer_class = NurseProfileSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'shift']
    search_fields = ['user__first_name', 'user__last_name', 'license_number']
    ordering_fields = ['user__first_name', 'created_at']
    ordering = ['user__first_name']

    def get_queryset(self):
        return NurseProfile.objects.select_related('user')

    @action(detail=False, methods=['get'])
    def by_shift(self, request):
        """Get nurses by shift"""
        shift = request.query_params.get('shift')
        if shift:
            queryset = self.get_queryset().filter(shift=shift)
        else:
            queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_department(self, request):
        """Get nurses by department"""
        department = request.query_params.get('department')
        if department:
            queryset = self.get_queryset().filter(department__icontains=department)
        else:
            queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
