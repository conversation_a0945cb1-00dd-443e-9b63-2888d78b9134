"""
Database Integrity Validation for HMS
Comprehensive validation of database relationships, constraints, and data consistency
"""

import os
import sys
import django
from datetime import datetime
from django.db import connection
from django.db.models import Count, Q
from django.core.exceptions import ValidationError

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()

from users.models import User
from patient_management.models import Patient, MedicalRecord, Prescription, LabTest
from staff_management.models import StaffProfile, LeaveRequest, Attendance
from appointment_system.models import Appointment, AppointmentSlot
from medical_system.models import Department, Ward, Bed, Admission
from billing_system.models import Bill, Payment
from inventory.models import Item, StockMovement


class DatabaseIntegrityValidator:
    """
    Comprehensive database integrity validator
    """
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.stats = {}
    
    def log_issue(self, category, message, severity='ERROR'):
        """Log an integrity issue"""
        issue = {
            'category': category,
            'message': message,
            'severity': severity,
            'timestamp': datetime.now()
        }
        
        if severity == 'ERROR':
            self.issues.append(issue)
        else:
            self.warnings.append(issue)
    
    def validate_user_data_integrity(self):
        """Validate user data integrity"""
        print("🔍 Validating User Data Integrity...")
        
        # Check for users without roles
        users_without_roles = User.objects.filter(Q(role__isnull=True) | Q(role='')).count()
        if users_without_roles > 0:
            self.log_issue('USER_DATA', f"Found {users_without_roles} users without assigned roles")
        
        # Check for users with invalid email formats
        users_invalid_email = User.objects.exclude(email__regex=r'^[^@]+@[^@]+\.[^@]+$').count()
        if users_invalid_email > 0:
            self.log_issue('USER_DATA', f"Found {users_invalid_email} users with invalid email formats")
        
        # Check for duplicate usernames
        duplicate_usernames = User.objects.values('username').annotate(
            count=Count('username')
        ).filter(count__gt=1).count()
        if duplicate_usernames > 0:
            self.log_issue('USER_DATA', f"Found {duplicate_usernames} duplicate usernames")
        
        # Check for duplicate emails
        duplicate_emails = User.objects.values('email').annotate(
            count=Count('email')
        ).filter(count__gt=1).count()
        if duplicate_emails > 0:
            self.log_issue('USER_DATA', f"Found {duplicate_emails} duplicate email addresses")
        
        # Check for inactive users with recent activity
        from django.utils import timezone
        from datetime import timedelta
        
        recent_date = timezone.now() - timedelta(days=30)
        inactive_with_recent_login = User.objects.filter(
            is_active=False,
            last_login__gte=recent_date
        ).count()
        if inactive_with_recent_login > 0:
            self.log_issue('USER_DATA', 
                          f"Found {inactive_with_recent_login} inactive users with recent login activity", 
                          'WARNING')
        
        self.stats['total_users'] = User.objects.count()
        self.stats['active_users'] = User.objects.filter(is_active=True).count()
        
        print(f"  ✓ Total Users: {self.stats['total_users']}")
        print(f"  ✓ Active Users: {self.stats['active_users']}")
    
    def validate_patient_data_integrity(self):
        """Validate patient data integrity"""
        print("🏥 Validating Patient Data Integrity...")
        
        # Check for orphaned patients (patients without users)
        orphaned_patients = Patient.objects.filter(user__isnull=True).count()
        if orphaned_patients > 0:
            self.log_issue('PATIENT_DATA', f"Found {orphaned_patients} orphaned patient records")
        
        # Check for patients with invalid patient ID format
        invalid_patient_ids = Patient.objects.exclude(patient_id__regex=r'^P\d{6}$').count()
        if invalid_patient_ids > 0:
            self.log_issue('PATIENT_DATA', f"Found {invalid_patient_ids} patients with invalid ID format")
        
        # Check for duplicate patient IDs
        duplicate_patient_ids = Patient.objects.values('patient_id').annotate(
            count=Count('patient_id')
        ).filter(count__gt=1).count()
        if duplicate_patient_ids > 0:
            self.log_issue('PATIENT_DATA', f"Found {duplicate_patient_ids} duplicate patient IDs")
        
        # Check for patients without blood group
        patients_no_blood_group = Patient.objects.filter(
            Q(blood_group__isnull=True) | Q(blood_group='')
        ).count()
        if patients_no_blood_group > 0:
            self.log_issue('PATIENT_DATA', 
                          f"Found {patients_no_blood_group} patients without blood group", 
                          'WARNING')
        
        # Check for patients with non-patient role users
        patients_wrong_role = Patient.objects.exclude(user__role=User.Role.PATIENT).count()
        if patients_wrong_role > 0:
            self.log_issue('PATIENT_DATA', f"Found {patients_wrong_role} patients with non-patient role users")
        
        self.stats['total_patients'] = Patient.objects.count()
        print(f"  ✓ Total Patients: {self.stats['total_patients']}")
    
    def validate_staff_data_integrity(self):
        """Validate staff data integrity"""
        print("👥 Validating Staff Data Integrity...")
        
        # Check for orphaned staff profiles
        orphaned_staff = StaffProfile.objects.filter(user__isnull=True).count()
        if orphaned_staff > 0:
            self.log_issue('STAFF_DATA', f"Found {orphaned_staff} orphaned staff profiles")
        
        # Check for duplicate employee IDs
        duplicate_employee_ids = StaffProfile.objects.values('employee_id').annotate(
            count=Count('employee_id')
        ).filter(count__gt=1).count()
        if duplicate_employee_ids > 0:
            self.log_issue('STAFF_DATA', f"Found {duplicate_employee_ids} duplicate employee IDs")
        
        # Check for staff with patient role users
        staff_patient_role = StaffProfile.objects.filter(user__role=User.Role.PATIENT).count()
        if staff_patient_role > 0:
            self.log_issue('STAFF_DATA', f"Found {staff_patient_role} staff profiles with patient role users")
        
        # Check for active staff without hire date
        staff_no_hire_date = StaffProfile.objects.filter(
            employment_status='active',
            hire_date__isnull=True
        ).count()
        if staff_no_hire_date > 0:
            self.log_issue('STAFF_DATA', 
                          f"Found {staff_no_hire_date} active staff without hire date", 
                          'WARNING')
        
        # Check for terminated staff without termination date
        terminated_no_date = StaffProfile.objects.filter(
            employment_status='terminated',
            termination_date__isnull=True
        ).count()
        if terminated_no_date > 0:
            self.log_issue('STAFF_DATA', 
                          f"Found {terminated_no_date} terminated staff without termination date")
        
        self.stats['total_staff'] = StaffProfile.objects.count()
        self.stats['active_staff'] = StaffProfile.objects.filter(employment_status='active').count()
        
        print(f"  ✓ Total Staff: {self.stats['total_staff']}")
        print(f"  ✓ Active Staff: {self.stats['active_staff']}")
    
    def validate_appointment_data_integrity(self):
        """Validate appointment data integrity"""
        print("📅 Validating Appointment Data Integrity...")
        
        # Check for appointments without patients
        appointments_no_patient = Appointment.objects.filter(patient__isnull=True).count()
        if appointments_no_patient > 0:
            self.log_issue('APPOINTMENT_DATA', f"Found {appointments_no_patient} appointments without patients")
        
        # Check for appointments without doctors
        appointments_no_doctor = Appointment.objects.filter(doctor__isnull=True).count()
        if appointments_no_doctor > 0:
            self.log_issue('APPOINTMENT_DATA', f"Found {appointments_no_doctor} appointments without doctors")
        
        # Check for appointments with invalid date/time combinations
        from django.utils import timezone
        past_scheduled_appointments = Appointment.objects.filter(
            appointment_date__lt=timezone.now().date(),
            status='scheduled'
        ).count()
        if past_scheduled_appointments > 0:
            self.log_issue('APPOINTMENT_DATA', 
                          f"Found {past_scheduled_appointments} past appointments still marked as scheduled", 
                          'WARNING')
        
        # Check for duplicate appointment IDs
        duplicate_appointment_ids = Appointment.objects.values('appointment_id').annotate(
            count=Count('appointment_id')
        ).filter(count__gt=1).count()
        if duplicate_appointment_ids > 0:
            self.log_issue('APPOINTMENT_DATA', f"Found {duplicate_appointment_ids} duplicate appointment IDs")
        
        self.stats['total_appointments'] = Appointment.objects.count()
        self.stats['active_appointments'] = Appointment.objects.filter(
            status__in=['scheduled', 'confirmed', 'in_progress']
        ).count()
        
        print(f"  ✓ Total Appointments: {self.stats['total_appointments']}")
        print(f"  ✓ Active Appointments: {self.stats['active_appointments']}")
    
    def validate_medical_system_integrity(self):
        """Validate medical system data integrity"""
        print("🏥 Validating Medical System Integrity...")
        
        # Check for wards without departments
        wards_no_department = Ward.objects.filter(department__isnull=True).count()
        if wards_no_department > 0:
            self.log_issue('MEDICAL_SYSTEM', f"Found {wards_no_department} wards without departments")
        
        # Check for beds without wards
        beds_no_ward = Bed.objects.filter(ward__isnull=True).count()
        if beds_no_ward > 0:
            self.log_issue('MEDICAL_SYSTEM', f"Found {beds_no_ward} beds without wards")
        
        # Check for duplicate bed numbers within wards
        duplicate_bed_numbers = Bed.objects.values('ward', 'bed_number').annotate(
            count=Count('id')
        ).filter(count__gt=1).count()
        if duplicate_bed_numbers > 0:
            self.log_issue('MEDICAL_SYSTEM', f"Found {duplicate_bed_numbers} duplicate bed numbers within wards")
        
        # Check for admissions without patients
        admissions_no_patient = Admission.objects.filter(patient__isnull=True).count()
        if admissions_no_patient > 0:
            self.log_issue('MEDICAL_SYSTEM', f"Found {admissions_no_patient} admissions without patients")
        
        # Check for admissions without beds
        admissions_no_bed = Admission.objects.filter(bed__isnull=True).count()
        if admissions_no_bed > 0:
            self.log_issue('MEDICAL_SYSTEM', f"Found {admissions_no_bed} admissions without beds")
        
        # Check for active admissions with discharge dates
        active_with_discharge = Admission.objects.filter(
            status='admitted',
            discharge_date__isnull=False
        ).count()
        if active_with_discharge > 0:
            self.log_issue('MEDICAL_SYSTEM', 
                          f"Found {active_with_discharge} active admissions with discharge dates")
        
        self.stats['total_departments'] = Department.objects.count()
        self.stats['total_wards'] = Ward.objects.count()
        self.stats['total_beds'] = Bed.objects.count()
        self.stats['occupied_beds'] = Bed.objects.filter(status='occupied').count()
        
        print(f"  ✓ Total Departments: {self.stats['total_departments']}")
        print(f"  ✓ Total Wards: {self.stats['total_wards']}")
        print(f"  ✓ Total Beds: {self.stats['total_beds']}")
        print(f"  ✓ Occupied Beds: {self.stats['occupied_beds']}")
    
    def validate_foreign_key_constraints(self):
        """Validate foreign key constraints"""
        print("🔗 Validating Foreign Key Constraints...")
        
        # This would typically be done at the database level,
        # but we can check for common issues
        
        # Check for medical records without patients
        records_no_patient = MedicalRecord.objects.filter(patient__isnull=True).count()
        if records_no_patient > 0:
            self.log_issue('FOREIGN_KEYS', f"Found {records_no_patient} medical records without patients")
        
        # Check for prescriptions without patients
        prescriptions_no_patient = Prescription.objects.filter(patient__isnull=True).count()
        if prescriptions_no_patient > 0:
            self.log_issue('FOREIGN_KEYS', f"Found {prescriptions_no_patient} prescriptions without patients")
        
        # Check for leave requests without staff
        leaves_no_staff = LeaveRequest.objects.filter(staff__isnull=True).count()
        if leaves_no_staff > 0:
            self.log_issue('FOREIGN_KEYS', f"Found {leaves_no_staff} leave requests without staff")
        
        print("  ✓ Foreign key constraints validated")
    
    def generate_integrity_report(self):
        """Generate comprehensive integrity report"""
        print("\n" + "=" * 80)
        print("🗄️ DATABASE INTEGRITY REPORT")
        print("=" * 80)
        
        print(f"Total Issues Found: {len(self.issues)}")
        print(f"Total Warnings: {len(self.warnings)}")
        
        if self.issues:
            print("\n🚨 CRITICAL ISSUES:")
            for issue in self.issues:
                print(f"  [{issue['category']}] {issue['message']}")
        
        if self.warnings:
            print("\n⚠️ WARNINGS:")
            for warning in self.warnings:
                print(f"  [{warning['category']}] {warning['message']}")
        
        print("\n📊 DATABASE STATISTICS:")
        for key, value in self.stats.items():
            print(f"  {key.replace('_', ' ').title()}: {value}")
        
        print("=" * 80)
        
        return len(self.issues) == 0
    
    def run_full_validation(self):
        """Run complete database integrity validation"""
        print("🗄️ Starting Database Integrity Validation...")
        print("-" * 60)
        
        self.validate_user_data_integrity()
        self.validate_patient_data_integrity()
        self.validate_staff_data_integrity()
        self.validate_appointment_data_integrity()
        self.validate_medical_system_integrity()
        self.validate_foreign_key_constraints()
        
        success = self.generate_integrity_report()
        
        if success:
            print("✅ Database integrity validation PASSED")
        else:
            print("❌ Database integrity validation FAILED")
        
        return success


def main():
    """Main function to run database integrity validation"""
    validator = DatabaseIntegrityValidator()
    success = validator.run_full_validation()
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
