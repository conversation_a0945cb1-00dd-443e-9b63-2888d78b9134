#!/usr/bin/env python3
"""
Fix Database Consistency Issues
Resolves patient-user mismatches and other data integrity issues
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
django.setup()

from users.models import User
from patient_management.models import Patient
from django.db import transaction

def fix_patient_consistency():
    """Fix patient-user consistency issues"""
    print("Analyzing patient-user consistency...")
    
    patient_users = User.objects.filter(role='patient')
    patients = Patient.objects.all()
    
    print(f"Patient Users: {patient_users.count()}")
    print(f"Patient Records: {patients.count()}")
    
    # Find users without patient records
    users_without_patients = []
    for user in patient_users:
        try:
            # Check if user has a patient profile
            patient = Patient.objects.get(user=user)
        except Patient.DoesNotExist:
            users_without_patients.append(user)
    
    print(f"Users without patient records: {len(users_without_patients)}")
    
    # Find patients without user records
    patients_without_users = []
    for patient in patients:
        if patient.user is None or patient.user.role != 'patient':
            patients_without_users.append(patient)
    
    print(f"Patient records without valid users: {len(patients_without_users)}")
    
    # Fix issues
    with transaction.atomic():
        # Create patient records for users without them
        for user in users_without_patients:
            print(f"Creating patient record for user: {user.username}")
            # Let the model generate the patient_id automatically
            Patient.objects.create(
                user=user,
                allergies="",
                chronic_conditions="",
                current_medications="",
                emergency_contact_name="",
                emergency_contact_phone=""
            )
        
        # Fix patients without valid users
        for patient in patients_without_users:
            if patient.user is None:
                print(f"Deleting orphaned patient record: {patient.patient_id}")
                patient.delete()
            elif patient.user.role != 'patient':
                print(f"Fixing user role for patient: {patient.patient_id}")
                patient.user.role = 'patient'
                patient.user.save()
    
    print("Patient consistency fixed!")

def fix_duplicate_patient_ids():
    """Fix duplicate patient IDs"""
    print("Checking for duplicate patient IDs...")
    
    from django.db.models import Count
    
    duplicates = Patient.objects.values('patient_id').annotate(
        count=Count('patient_id')
    ).filter(count__gt=1)
    
    if duplicates:
        print(f"Found {len(duplicates)} duplicate patient IDs")
        
        with transaction.atomic():
            for dup in duplicates:
                patient_id = dup['patient_id']
                patients = Patient.objects.filter(patient_id=patient_id)
                
                # Keep the first one, update the rest
                for i, patient in enumerate(patients[1:], 1):
                    new_id = f"{patient_id}_{i}"
                    print(f"Updating duplicate patient ID {patient_id} to {new_id}")
                    patient.patient_id = new_id
                    patient.save()
    else:
        print("No duplicate patient IDs found")

def validate_user_roles():
    """Validate user roles are consistent"""
    print("Validating user roles...")
    
    # Check for invalid roles
    valid_roles = [choice[0] for choice in User.Role.choices]
    invalid_users = User.objects.exclude(role__in=valid_roles)
    
    if invalid_users.exists():
        print(f"Found {invalid_users.count()} users with invalid roles")
        for user in invalid_users:
            print(f"  - {user.username}: {user.role}")
    else:
        print("All user roles are valid")
    
    # Check admin consistency
    admin_users = User.objects.filter(role='admin')
    superusers = User.objects.filter(is_superuser=True)
    
    print(f"Admin role users: {admin_users.count()}")
    print(f"Django superusers: {superusers.count()}")
    
    # Check for admins without superuser status
    admins_not_super = admin_users.filter(is_superuser=False)
    if admins_not_super.exists():
        print(f"Admin users without superuser status: {admins_not_super.count()}")
        for user in admins_not_super:
            print(f"  - {user.username}")

def generate_missing_patient_ids():
    """Generate patient IDs for patients that don't have them"""
    print("Checking for missing patient IDs...")
    
    patients_without_ids = Patient.objects.filter(patient_id__isnull=True) | Patient.objects.filter(patient_id="")
    
    if patients_without_ids.exists():
        print(f"Found {patients_without_ids.count()} patients without IDs")
        
        with transaction.atomic():
            for patient in patients_without_ids:
                # Generate new patient ID
                new_id = f"P{str(patient.id).zfill(6)}"
                
                # Make sure it's unique
                while Patient.objects.filter(patient_id=new_id).exists():
                    new_id = f"P{str(patient.id).zfill(6)}_{Patient.objects.filter(patient_id__startswith=f'P{str(patient.id).zfill(6)}').count()}"
                
                patient.patient_id = new_id
                patient.save()
                print(f"Generated patient ID {new_id} for patient {patient.id}")
    else:
        print("All patients have valid IDs")

def main():
    """Run all database consistency fixes"""
    print("HMS Database Consistency Fixer")
    print("=" * 40)
    
    try:
        fix_patient_consistency()
        print()
        
        fix_duplicate_patient_ids()
        print()
        
        validate_user_roles()
        print()
        
        generate_missing_patient_ids()
        print()
        
        print("Database consistency check completed!")
        
        # Final verification
        print("\nFinal counts:")
        patient_users = User.objects.filter(role='patient').count()
        patients = Patient.objects.count()
        print(f"Patient Users: {patient_users}")
        print(f"Patient Records: {patients}")
        
        if patient_users == patients:
            print("[SUCCESS] Patient consistency achieved!")
        else:
            print("[ERROR] Patient consistency still needs work")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
