import React, { useEffect, useState } from 'react';
import { 
  <PERSON>ting<PERSON>, 
  Cpu, 
  Users, 
  Workflow, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  <PERSON>fresh<PERSON><PERSON>,
  Save,
  Eye,
  EyeOff
} from 'lucide-react';

interface AIConfig {
  mode: string;
  models: Record<string, any>;
  agents: Record<string, any>;
  workflows: Record<string, any>;
  deployment: any;
}

interface Profile {
  models: string[];
  agents: string[];
  workflows: string[];
}

interface SystemStatus {
  deployment_mode: string;
  total_models: number;
  active_models: number;
  total_agents: number;
  active_agents: number;
  total_workflows: number;
  active_workflows: number;
  api_keys_configured: number;
  api_keys_total: number;
}

const AIConfigManager: React.FC = () => {
  const [config, setConfig] = useState<AIConfig | null>(null);
  const [profiles, setProfiles] = useState<Record<string, Profile>>({});
  const [currentProfile, setCurrentProfile] = useState<string>('');
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [apiKeysStatus, setApiKeysStatus] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'models' | 'agents' | 'workflows' | 'profiles'>('overview');
  const [showApiKeys, setShowApiKeys] = useState(false);

  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        fetchConfig(),
        fetchProfiles(),
        fetchSystemStatus()
      ]);
    } catch (error) {
      console.error('Failed to fetch AI configuration data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchConfig = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/ai/config/current_config/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConfig(data.config);
      }
    } catch (error) {
      console.error('Failed to fetch config:', error);
    }
  };

  const fetchProfiles = async () => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/ai/config/profiles/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setProfiles(data.profiles);
        setCurrentProfile(data.current_profile);
      }
    } catch (error) {
      console.error('Failed to fetch profiles:', error);
    }
  };

  const fetchSystemStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/ai/config/system_status/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setSystemStatus(data.system_status);
        setApiKeysStatus(data.api_keys_status);
      }
    } catch (error) {
      console.error('Failed to fetch system status:', error);
    }
  };

  const applyProfile = async (profileName: string) => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/ai/config/apply_profile/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ profile_name: profileName })
      });
      
      if (response.ok) {
        const data = await response.json();
        setCurrentProfile(profileName);
        await fetchAllData(); // Refresh all data
        alert(`Profile "${profileName}" applied successfully!`);
      } else {
        const errorData = await response.json();
        alert(`Failed to apply profile: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Failed to apply profile:', error);
      alert('Failed to apply profile');
    }
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'text-primary bg-primary/10' : 'text-destructive bg-destructive/10';
  };

  const getProfileBadgeColor = (profileName: string) => {
    const colors = {
      minimal: 'bg-muted text-muted-foreground',
      standard: 'bg-primary/10 text-primary',
      advanced: 'bg-primary/20 text-primary',
      enterprise: 'bg-primary/30 text-primary',
      custom: 'bg-accent text-accent-foreground'
    };
    return colors[profileName as keyof typeof colors] || colors.custom;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6 text-white">
        <div className="flex items-center gap-3 mb-4">
          <Settings className="w-8 h-8" />
          <div>
            <h1 className="text-2xl font-bold">AI Configuration Manager</h1>
            <p className="text-blue-100">
              Centralized configuration for all AI services and multi-agent systems
            </p>
          </div>
        </div>
        
        {systemStatus && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
            <div className="bg-white/10 rounded-lg p-3">
              <div className="text-sm text-blue-100">Deployment Mode</div>
              <div className="text-lg font-semibold">{systemStatus.deployment_mode}</div>
            </div>
            <div className="bg-white/10 rounded-lg p-3">
              <div className="text-sm text-blue-100">Active Models</div>
              <div className="text-lg font-semibold">{systemStatus.active_models}/{systemStatus.total_models}</div>
            </div>
            <div className="bg-white/10 rounded-lg p-3">
              <div className="text-sm text-blue-100">Active Agents</div>
              <div className="text-lg font-semibold">{systemStatus.active_agents}/{systemStatus.total_agents}</div>
            </div>
            <div className="bg-white/10 rounded-lg p-3">
              <div className="text-sm text-blue-100">API Keys</div>
              <div className="text-lg font-semibold">{systemStatus.api_keys_configured}/{systemStatus.api_keys_total}</div>
            </div>
          </div>
        )}
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: <Eye className="w-4 h-4" /> },
            { id: 'models', label: 'Models', icon: <Cpu className="w-4 h-4" /> },
            { id: 'agents', label: 'Agents', icon: <Users className="w-4 h-4" /> },
            { id: 'workflows', label: 'Workflows', icon: <Workflow className="w-4 h-4" /> },
            { id: 'profiles', label: 'Profiles', icon: <Settings className="w-4 h-4" /> }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Current Profile */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">Current Profile</h3>
                <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getProfileBadgeColor(currentProfile)}`}>
                  {currentProfile}
                </div>
              </div>

              {/* API Keys Status */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900 dark:text-white">API Keys</h3>
                  <button
                    onClick={() => setShowApiKeys(!showApiKeys)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    {showApiKeys ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
                <div className="space-y-2">
                  {Object.entries(apiKeysStatus).map(([model, hasKey]) => (
                    <div key={model} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">{model}</span>
                      {hasKey ? (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-600" />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* System Health */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">System Health</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Configuration Loaded</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Agents Initialized</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Workflows Active</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Profiles Tab */}
        {activeTab === 'profiles' && (
          <div className="p-6">
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Configuration Profiles
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Choose a pre-configured profile to quickly set up your AI system
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Object.entries(profiles).map(([profileName, profile]) => (
                <div
                  key={profileName}
                  className={`border rounded-lg p-4 ${
                    currentProfile === profileName
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-600'
                  }`}
                >
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-900 dark:text-white capitalize">
                      {profileName}
                    </h4>
                    {currentProfile === profileName && (
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        Active
                      </span>
                    )}
                  </div>
                  
                  <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                    <div>Models: {profile.models?.length || 0}</div>
                    <div>Agents: {profile.agents?.length || 0}</div>
                    <div>Workflows: {profile.workflows?.length || 0}</div>
                  </div>
                  
                  {currentProfile !== profileName && (
                    <button
                      onClick={() => applyProfile(profileName)}
                      className="w-full px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Apply Profile
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Other tabs would be implemented similarly */}
        {activeTab !== 'overview' && activeTab !== 'profiles' && (
          <div className="p-6 text-center">
            <div className="text-gray-500 dark:text-gray-400">
              {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} configuration coming soon...
            </div>
          </div>
        )}
      </div>

      {/* Refresh Button */}
      <div className="flex justify-end">
        <button
          onClick={fetchAllData}
          className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          Refresh Configuration
        </button>
      </div>
    </div>
  );
};

export default AIConfigManager;
