import React, {
    useEffect, useState
  } from 'react';
  import {
    useDispatch, useSelector
  } from 'react-redux';
  import {
    Brain, Stethoscope, Pill, TrendingUp, MessageSquare, Activity, Shield, Zap
  } from 'lucide-react';
  import type {
    RootState, AppDispatch
  } from '../../store';
  import {
    fetchHealthStatus, fetchConversations
  } from '../../store/slices/aiSlice';
  import { getStatusClass } from '../../utils/styleUtils';
  interface AIMetric {
    label: string;
  value: string | number;
  change?: string;
  trend?: 'up' | 'down' | 'stable';
  icon: React.ReactNode;
  color: string;
  }

const AIDashboard: React.FC = () => {
    const dispatch = useDispatch
    <AppDispatch>();
  const {
    conversations, healthStatus, isLoading
  } =
  useSelector((state: RootState) => state.ai);
  const [metrics, setMetrics] = useState
    <AIMetric[]>([]);
  useEffect(() => {
    dispatch(fetchHealthStatus());
  dispatch(fetchConversations());
  }, [dispatch]);
  useEffect(() => { // Calculate metrics from conversations and health status

const totalConversations = conversations.length;
  const activeConversations = conversations.filter(c => c.is_active).length;
  const diagnosisConversations = conversations.filter(c => c.conversation_type === 'diagnosis').length;
  const treatmentConversations = conversations.filter(c => c.conversation_type === 'treatment').length;
  const newMetrics: AIMetric[] = [ {
    label: 'Total AI Conversations', value: totalConversations, change: '+12%', trend: 'up', icon:
    <MessageSquare className="w-5 h-5" />, color: 'bg-primary'
  }, {
    label: 'Active Sessions', value: activeConversations, change: '+5%', trend: 'up', icon:
    <Activity className="w-5 h-5" />, color: 'bg-primary'
  }, {
    label: 'AI Diagnoses Generated', value: diagnosisConversations, change: '+8%', trend: 'up', icon:
    <Stethoscope className="w-5 h-5" />, color: 'bg-primary'
  }, {
    label: 'Treatment Plans Created', value: treatmentConversations, change: '+15%', trend: 'up', icon:
    <Brain className="w-5 h-5" />, color: 'bg-primary'
  }, {
    label: 'AI System Health', value: healthStatus?.overall_status === 'healthy' ? 'Optimal' : 'Degraded', icon:
    <Shield className="w-5 h-5" />, color: healthStatus?.overall_status === 'healthy' ? 'bg-primary' : 'bg-destructive'
  }, {
    label: 'Response Time', value: '1.2s', change: '-0.3s', trend: 'down', icon:
    <Zap className="w-5 h-5" />, color: 'bg-primary'
  } ];
  setMetrics(newMetrics);
  }, [conversations, healthStatus]);
  const getServiceStatus = (service: string) => {
    if (!healthStatus) return 'unknown';
  switch (service) {
    case 'gemini': return healthStatus.gemini_ai?.status || 'unknown';
  case 'langgraph': return healthStatus.langgraph?.status || 'unknown';
  case 'ml': return healthStatus.ml_service?.status || 'unknown';
  default: return 'unknown';
  }
  };
  // Using unified style system - no need for custom color function
  const getTrendIcon = (trend?: string) => {
    switch (trend) {
    case 'up': return
    <TrendingUp className="w-3 h-3 text-primary" />;
  case 'down': return
    <TrendingUp className="w-3 h-3 text-destructive rotate-180" />;
  default: return null;
  }
  };
  if (isLoading) {
    return ( <div className="flex items-center justify-center h-64"> <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div> </div> );
  }
  return ( <div className="space-y-6"> {/* Header */
  } <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-6 text-white"> <div className="flex items-center gap-3 mb-4">
    <Brain className="w-8 h-8" /> <div> <h1 className="text-2xl font-bold">AI Services Dashboard</h1> <p className="text-blue-100"> Monitor and manage AI-powered medical services </p> </div> </div> <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6"> <div className="bg-background/10 rounded-lg p-4"> <div className="flex items-center gap-2 mb-2">
    <Brain className="w-5 h-5" /> <span className="font-medium">Gemini AI</span> </div> <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
    getStatusClass(getServiceStatus('gemini'))
  }`
  }> {
    getServiceStatus('gemini')
  } </div> </div> <div className="bg-background/10 rounded-lg p-4"> <div className="flex items-center gap-2 mb-2">
    <Activity className="w-5 h-5" /> <span className="font-medium">LangGraph</span> </div> <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
    getStatusClass(getServiceStatus('langgraph'))
  }`
  }> {
    getServiceStatus('langgraph')
  } </div> </div> <div className="bg-background/10 rounded-lg p-4"> <div className="flex items-center gap-2 mb-2">
    <TrendingUp className="w-5 h-5" /> <span className="font-medium">ML Models</span> </div> <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
    getStatusClass(getServiceStatus('ml'))
  }`
  }> {
    getServiceStatus('ml')
  } </div> </div> </div> </div> {/* Metrics Grid */
  } <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> {
    metrics.map((metric, index) => ( <div key={
    index
  } className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700" > <div className="flex items-center justify-between mb-4"> <div className={`p-2 rounded-lg ${
    metric.color
  } text-white`
  }> {
    metric.icon
  } </div> {
    metric.trend && ( <div className="flex items-center gap-1"> {
    getTrendIcon(metric.trend)
  } <span className={`text-xs font-medium ${
    metric.trend === 'up' ? 'text-emerald-700 dark:text-emerald-400' : 'text-rose-700 dark:text-rose-400'
  }`
  }> {
    metric.change
  } </span> </div> )
  } </div> <div> <p className="text-2xl font-bold text-foreground dark:text-white mb-1"> {
    metric.value
  } </p> <p className="text-sm text-muted-foreground dark:text-gray-400"> {
    metric.label
  } </p> </div> </div> ))
  } </div> {/* Recent Activity */
  } <div className="bg-background dark:bg-gray-800 rounded-lg shadow-sm border border-border dark:border-gray-700"> <div className="p-6 border-b border-border dark:border-gray-700"> <h2 className="text-lg font-semibold text-foreground dark:text-white"> Recent AI Activity </h2> </div> <div className="p-6"> {
    conversations.slice(0, 5).map((conversation) => ( <div key={
    conversation.conversation_id
  } className="flex items-center gap-4 py-3 border-b border-gray-100 dark:border-gray-700 last:border-0" > <div className={`p-2 rounded-lg ${
    conversation.conversation_type === 'diagnosis' ? 'bg-primary/10 text-primary' : conversation.conversation_type === 'treatment' ? 'bg-primary/10 text-primary' : 'bg-muted text-muted-foreground'
  }`
  }> {
    conversation.conversation_type === 'diagnosis' ? (
    <Stethoscope className="w-4 h-4" /> ) : conversation.conversation_type === 'treatment' ? (
    <Brain className="w-4 h-4" /> ) : (
    <MessageSquare className="w-4 h-4" /> )
  } </div> <div className="flex-1"> <p className="font-medium text-foreground dark:text-white"> {
    conversation.title || `${
    conversation.conversation_type
  } conversation`
  } </p> <p className="text-sm text-muted-foreground dark:text-gray-400"> {
    conversation.patient_name ? `Patient: ${
    conversation.patient_name
  }` : 'General consultation'
  } </p> </div> <div className="text-right"> <p className="text-sm text-muted-foreground dark:text-gray-400"> {
    new Date(conversation.updated_at).toLocaleDateString()
  } </p> <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
    conversation.is_active ? getStatusClass('active') : getStatusClass('completed')
  }`
  }> {
    conversation.is_active ? 'Active' : 'Completed'
  } </div> </div> </div> ))
  } {
    conversations.length === 0 && ( <div className="text-center py-8">
    <MessageSquare className="w-12 h-12 mx-auto text-gray-400 mb-4" /> <p className="text-muted-foreground dark:text-gray-400"> No AI conversations yet </p> <p className="text-sm text-gray-500 dark:text-gray-500 mt-2"> Start using AI services to see activity here </p> </div> )
  } </div> </div> </div> );
  };
  export default AIDashboard;
