import React, {
    useEffect, useState
  } from 'react';
  import {
    Users, Activity, Clock, CheckCircle, AlertTriangle, Brain, Stethoscope, Pill, Shield, Zap, TrendingUp, BarChart3
  } from 'lucide-react';
  import { getStatusClass } from '../../utils/styleUtils';
  interface Agent {
    name: string;
  status: string;
  capabilities: string[];
  ai_available: boolean;
  } interface AgentPerformance {
    success_rate: number;
  uptime: number;
  avg_processing_time: number;
  total_tasks: number;
  } interface SystemMetrics {
    total_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  overall_success_rate: number;
  average_processing_time: number;
  average_confidence_score: number;
  recent_cases_7_days: number;
  }

const MultiAgentDashboard: React.FC = () => {
    const [agents, setAgents] = useState
    <Record<string, Agent>>({
  });
  const [performance, setPerformance] = useState
    <Record<string, AgentPerformance>>({
  });
  const [systemMetrics, setSystemMetrics] = useState
    <SystemMetrics | null>(null);
  const [isLoading, setIsLoading] =
  useState(true);
  useEffect(() => {
    fetchAgentStatus();
  fetchSystemMetrics();
  }, []);
  const fetchAgentStatus = async () => {
    try {
    const token = localStorage.getItem('token');
  const response = await fetch('/api/ai/multi-agent/cases/agent_status/', {
    headers: { 'Authorization': `Bearer ${
    token
  }`, 'Content-Type': 'application/json'
  }
  });
  if (response.ok) {
    const data = await response.json();
  setAgents(data.agent_status || {
  });
  setPerformance(data.performance_metrics || {
  });
  }
  } catch (error) {
    console.error('Failed to fetch agent status:', error);
  }
  };
  const fetchSystemMetrics = async () => {
    try {
    const token = localStorage.getItem('token');
  const response = await fetch('/api/ai/multi-agent/performance/system_metrics/', {
    headers: { 'Authorization': `Bearer ${
    token
  }`, 'Content-Type': 'application/json'
  }
  });
  if (response.ok) {
    const data = await response.json();
  setSystemMetrics(data.system_overview);
  }
  } catch (error) {
    console.error('Failed to fetch system metrics:', error);
  } finally {
    setIsLoading(false);
  }
  };
  const getAgentIcon = (agentRole: string) => {
    switch (agentRole) {
    case 'triage': return
    <Activity className="w-5 h-5" />;
  case 'diagnostic': return
    <Stethoscope className="w-5 h-5" />;
  case 'treatment': return
    <Brain className="w-5 h-5" />;
  case 'pharmacist': return
    <Pill className="w-5 h-5" />;
  case 'emergency': return
    <AlertTriangle className="w-5 h-5" />;
  case 'coordinator': return
    <Users className="w-5 h-5" />;
  default: return
    <Brain className="w-5 h-5" />;
  }
  };
  // Using unified style system - no need for custom color function
  const getPerformanceGrade = (successRate: number) => {
    if (successRate >= 95) return {
    grade: 'A+', color: 'text-emerald-700 dark:text-emerald-400'
  };
  if (successRate >= 90) return {
    grade: 'A', color: 'text-emerald-700 dark:text-emerald-400'
  };
  if (successRate >= 80) return {
    grade: 'B', color: 'text-sky-700 dark:text-sky-400'
  };
  if (successRate >= 70) return {
    grade: 'C', color: 'text-amber-700 dark:text-amber-400'
  };
  return {
    grade: 'D', color: 'text-rose-700 dark:text-rose-400'
  };
  };
  if (isLoading) {
    return ( <div className="flex items-center justify-center h-64"> <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div> </div> );
  }
  return ( <div className="space-y-6"> {/* Header */
  } <div className="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg p-6 text-white"> <div className="flex items-center gap-3 mb-4">
    <Users className="w-8 h-8" /> <div> <h1 className="text-2xl font-bold">Multi-Agent AI System</h1> <p className="text-purple-100"> Specialized AI agents working together for comprehensive medical care </p> </div> </div> </div> {/* System Overview */
  } {
    systemMetrics && ( <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"> <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <div className="flex items-center justify-between mb-4"> <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
    <BarChart3 className="w-5 h-5 text-sky-700 dark:text-sky-400 dark:text-blue-400" /> </div> <span className="text-2xl font-bold text-foreground dark:text-white"> {
    systemMetrics.total_tasks
  } </span> </div> <p className="text-sm text-muted-foreground dark:text-gray-400">Total Tasks Processed</p> </div> <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <div className="flex items-center justify-between mb-4"> <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
    <CheckCircle className="w-5 h-5 text-emerald-700 dark:text-emerald-400 dark:text-green-400" /> </div> <span className="text-2xl font-bold text-foreground dark:text-white"> {
    systemMetrics.overall_success_rate.toFixed(1)
  }% </span> </div> <p className="text-sm text-muted-foreground dark:text-gray-400">Success Rate</p> </div> <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <div className="flex items-center justify-between mb-4"> <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
    <Clock className="w-5 h-5 text-orange-600 dark:text-orange-400" /> </div> <span className="text-2xl font-bold text-foreground dark:text-white"> {(typeof systemMetrics.average_processing_time === 'number' ? systemMetrics.average_processing_time.toFixed(1) : '0.0')
  }s </span> </div> <p className="text-sm text-muted-foreground dark:text-gray-400">Avg Processing Time</p> </div> <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <div className="flex items-center justify-between mb-4"> <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
    <TrendingUp className="w-5 h-5 text-purple-600 dark:text-purple-400" /> </div> <span className="text-2xl font-bold text-foreground dark:text-white"> {
    systemMetrics.recent_cases_7_days
  } </span> </div> <p className="text-sm text-muted-foreground dark:text-gray-400">Cases This Week</p> </div> </div> )
  } {/* Agent Status Grid */
  } <div className="bg-background dark:bg-gray-800 rounded-lg shadow-sm border border-border dark:border-gray-700"> <div className="p-6 border-b border-border dark:border-gray-700"> <h2 className="text-lg font-semibold text-foreground dark:text-white"> Agent Status & Performance </h2> </div> <div className="p-6"> <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> {
    Object.entries(agents).map(([agentRole, agent]) => {
    const perf = performance[agentRole];
  const grade = perf ? getPerformanceGrade(perf.success_rate) : {
    grade: 'N/A', color: 'text-gray-500'
  };
  return ( <div key={
    agentRole
  } className="bg-muted dark:bg-gray-700 rounded-lg p-4 border border-border dark:border-gray-600" > <div className="flex items-center gap-3 mb-3"> <div className="p-2 bg-background dark:bg-gray-800 rounded-lg"> {
    getAgentIcon(agentRole)
  } </div> <div className="flex-1"> <h3 className="font-medium text-foreground dark:text-white"> {
    agent.name
  } </h3> <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
    getStatusClass(agent.status)
  }`
  }> {
    agent.status
  } </div> </div> <div className={`text-lg font-bold ${
    grade.color
  }`
  }> {
    grade.grade
  } </div> </div> {
    perf && ( <div className="space-y-2 text-sm"> <div className="flex justify-between"> <span className="text-muted-foreground dark:text-gray-400">Success Rate:</span> <span className="font-medium text-foreground dark:text-white"> {(typeof perf.success_rate === 'number' ? perf.success_rate.toFixed(1) : '0.0')
  }% </span> </div> <div className="flex justify-between"> <span className="text-muted-foreground dark:text-gray-400">Uptime:</span> <span className="font-medium text-foreground dark:text-white"> {(typeof perf.uptime === 'number' ? perf.uptime.toFixed(1) : '0.0')
  }% </span> </div> <div className="flex justify-between"> <span className="text-muted-foreground dark:text-gray-400">Avg Time:</span> <span className="font-medium text-foreground dark:text-white"> {(typeof perf.avg_processing_time === 'number' ? perf.avg_processing_time.toFixed(1) : '0.0')
  }s </span> </div> <div className="flex justify-between"> <span className="text-muted-foreground dark:text-gray-400">Tasks:</span> <span className="font-medium text-foreground dark:text-white"> {
    perf.total_tasks
  } </span> </div> </div> )
  } <div className="mt-3 pt-3 border-t border-border dark:border-gray-600"> <div className="flex items-center gap-2"> <div className={`w-2 h-2 rounded-full ${
    agent.ai_available ? 'bg-primary' : 'bg-destructive'
  }`
  }></div> <span className="text-xs text-muted-foreground dark:text-gray-400"> AI {
    agent.ai_available ? 'Available' : 'Unavailable'
  } </span> </div> </div> </div> );
  })
  } </div> </div> </div> {/* Capabilities Overview */
  } <div className="bg-background dark:bg-gray-800 rounded-lg shadow-sm border border-border dark:border-gray-700"> <div className="p-6 border-b border-border dark:border-gray-700"> <h2 className="text-lg font-semibold text-foreground dark:text-white"> System Capabilities </h2> </div> <div className="p-6"> <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"> <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
    <Activity className="w-5 h-5 text-sky-700 dark:text-sky-400 dark:text-blue-400" /> <span className="text-sm font-medium text-foreground dark:text-white"> Intelligent Triage & Prioritization </span> </div> <div className="flex items-center gap-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
    <Stethoscope className="w-5 h-5 text-purple-600 dark:text-purple-400" /> <span className="text-sm font-medium text-foreground dark:text-white"> AI-Powered Diagnosis </span> </div> <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
    <Brain className="w-5 h-5 text-emerald-700 dark:text-emerald-400 dark:text-green-400" /> <span className="text-sm font-medium text-foreground dark:text-white"> Treatment Planning </span> </div> <div className="flex items-center gap-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
    <Pill className="w-5 h-5 text-orange-600 dark:text-orange-400" /> <span className="text-sm font-medium text-foreground dark:text-white"> Medication Safety </span> </div> <div className="flex items-center gap-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
    <AlertTriangle className="w-5 h-5 text-rose-700 dark:text-rose-400 dark:text-red-400" /> <span className="text-sm font-medium text-foreground dark:text-white"> Emergency Response </span> </div> <div className="flex items-center gap-3 p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
    <Users className="w-5 h-5 text-indigo-600 dark:text-indigo-400" /> <span className="text-sm font-medium text-foreground dark:text-white"> Care Coordination </span> </div> </div> </div> </div> </div> );
  };
  export default MultiAgentDashboard;
