import React from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    <PERSON>
  } from 'react-router-dom';
  import {
    Shield, ArrowLeft, Building, Users, Lock
  } from 'lucide-react';
  import {
    Button
  } from '../ui/Button';
  import {
    Card, CardContent, CardDescription, CardHeader, CardTitle
  } from '../ui/card';
  import LanguageSwitcher from '../ui/LanguageSwitcher';
  const RestrictedAccess: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 flex items-center justify-center p-6"> {/* Language Switcher */
  } <div className="absolute top-6 right-6">
    <LanguageSwitcher /> </div> <div className="w-full max-w-md">
    <Card className="glass border-0 shadow-2xl">
    <CardHeader className="text-center pb-6"> <div className="flex items-center justify-center mb-6"> <div className="w-16 h-16 bg-gradient-to-br from-destructive to-destructive rounded-xl flex items-center justify-center shadow-lg">
    <Shield className="w-8 h-8 text-destructive-foreground" /> </div> </div>
    <CardTitle className="text-2xl font-bold macos-text-primary"> Access Restricted
    </CardTitle>
    <CardDescription className="macos-text-secondary text-base"> Hospital Management System
    </CardDescription>
    </CardHeader>
    <CardContent className="space-y-6"> {/* Main Message */
  } <div className="text-center space-y-4"> <div className="p-4 bg-destructive/10 border rounded-xl">
    <Lock className="w-8 h-8 text-destructive mx-auto mb-2" /> <h3 className="font-semibold text-destructive mb-2"> Registration Not Available </h3> <p className="text-sm text-destructive"> Public registration is not permitted in this hospital management system. </p> </div> <div className="space-y-3 text-sm macos-text-secondary"> <div className="flex items-center gap-3 p-3 bg-primary/10 rounded-lg">
    <Building className="w-5 h-5 text-primary flex-shrink-0" /> <span>This is a secure hospital management system</span> </div> <div className="flex items-center gap-3 p-3 bg-primary/10 rounded-lg">
    <Users className="w-5 h-5 text-primary flex-shrink-0" /> <span>Only authorized personnel can create accounts</span> </div> <div className="flex items-center gap-3 p-3 bg-primary/10 rounded-lg">
    <Shield className="w-5 h-5 text-primary flex-shrink-0" /> <span>Contact your system administrator for access</span> </div> </div> </div> {/* Instructions */
  } <div className="bg-muted dark:bg-gray-800/50 rounded-xl p-4 space-y-3"> <h4 className="font-semibold macos-text-primary text-sm"> Need Access? </h4> <ul className="text-sm macos-text-secondary space-y-2"> <li className="flex items-start gap-2"> <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span> <span>Contact your hospital's IT administrator</span> </li> <li className="flex items-start gap-2"> <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span> <span>Provide your role and department information</span> </li> <li className="flex items-start gap-2"> <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span> <span>Wait for account creation and credentials</span> </li> </ul> </div> {/* Action Buttons */
  } <div className="space-y-3">
    <Link to="/login" className="block">
    <Button className="w-full h-12 text-base font-semibold" variant="glass"> <div className="flex items-center gap-2">
    <Lock className="w-5 h-5" /> Sign In to Existing Account </div>
    </Button>
    </Link>
    <Link to="/" className="block">
    <Button variant="outline" className="w-full h-12 text-base font-semibold glass border-border/50 dark:border-gray-700/50" > <div className="flex items-center gap-2">
    <ArrowLeft className="w-5 h-5" /> Back to Home </div>
    </Button>
    </Link> </div> {/* Contact Information */
  } <div className="text-center pt-4 border-t border-border/50 dark:border-gray-700/50"> <p className="text-xs macos-text-tertiary"> For technical support, contact your IT department </p> </div>
    </CardContent>
    </Card> </div> </div> );
  };
  export default RestrictedAccess;
