import React, {
    useState, useEffect
  } from 'react';
  import {
    useSelector
  } from 'react-redux';
  import type {
    RootState
  } from '../../store';
  import billingService, {
    Invoice
  } from '../../services/billingService';
  import {
    Button
  } from '../ui/Button';
  interface BillingSummary {
    total_invoices: number;
  total_amount: number;
  total_paid: number;
  total_outstanding: number;
  overdue_count: number;
  }

const BillingDashboard: React.FC = () => {
    const {
    user
  } =
  useSelector((state: RootState) => state.auth);
  const [invoices, setInvoices] = useState
    <Invoice[]>([]);
  const [summary, setSummary] = useState
    <BillingSummary | null>(null);
  const [loading, setLoading] =
  useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedInvoice, setSelectedInvoice] = useState
    <Invoice | null>(null);
  const [filter, setFilter] =
  useState('all');
  useEffect(() => {
    loadData();
  }, [filter]);
  const loadData = async () => {
    try {
    setLoading(true); // Load summary

const summaryData = await billingService.getBillingSummary();
  setSummary(summaryData); // Load invoices based on filter

let invoiceData;
  switch (filter) {
    case 'unpaid': invoiceData = await billingService.getUnpaidInvoices();
  break;
  case 'overdue': invoiceData = await billingService.getOverdueInvoices();
  break;
  default: invoiceData = await billingService.getInvoices();
  } setInvoices(invoiceData.results || invoiceData);
  setError(null);
  } catch (err) {
    setError('Failed to load billing data');
  console.error('Error loading billing data:', err);
  } finally {
    setLoading(false);
  }
  };
  const handleMarkPaid = async (invoiceId: number) => {
    try {
    await billingService.markInvoicePaid(invoiceId);
  await loadData();
  } catch (err) {
    setError('Failed to mark invoice as paid');
  console.error('Error marking invoice as paid:', err);
  }
  };
  const handleMarkOverdue = async (invoiceId: number) => {
    try {
    await billingService.markInvoiceOverdue(invoiceId);
  await loadData();
  } catch (err) {
    setError('Failed to mark invoice as overdue');
  console.error('Error marking invoice as overdue:', err);
  }
  };
  const getStatusColor = (status: string) => {
    switch (status) {
    case 'draft': return 'bg-muted text-foreground';
  case 'sent': return 'status-info';
  case 'paid': return 'status-success';
  case 'partial': return 'status-warning';
  case 'overdue': return 'status-error';
  case 'cancelled': return 'bg-muted text-foreground';
  default: return 'bg-muted text-foreground';
  }
  };
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
    style: 'currency', currency: 'USD'
  }).format(amount);
  };
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };
  if (loading) {
    return ( <div className="flex justify-center items-center h-64"> <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div> </div> );
  }
  return ( <div className="space-y-6"> {/* Header */
  } <div className="flex justify-between items-center"> <h1 className="text-2xl font-bold text-foreground">Billing Dashboard</h1>
    <Button className="bg-blue-600 hover:bg-blue-700 text-white"> Create Invoice
    </Button> </div> {/* Summary Cards */
  } {
    summary && ( <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4"> <div className="bg-background overflow-hidden shadow rounded-lg"> <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"> <span className="text-white text-sm font-medium">📄</span> </div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 truncate"> Total Invoices </dt> <dd className="text-lg font-medium text-foreground"> {
    summary.total_invoices
  } </dd> </dl> </div> </div> </div> </div> <div className="bg-background overflow-hidden shadow rounded-lg"> <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"> <span className="text-white text-sm font-medium">💰</span> </div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 truncate"> Total Amount </dt> <dd className="text-lg font-medium text-foreground"> {
    formatCurrency(summary.total_amount)
  } </dd> </dl> </div> </div> </div> </div> <div className="bg-background overflow-hidden shadow rounded-lg"> <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="w-8 h-8 bg-green-600 rounded-md flex items-center justify-center"> <span className="text-white text-sm font-medium">✓</span> </div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 truncate"> Total Paid </dt> <dd className="text-lg font-medium text-foreground"> {
    formatCurrency(summary.total_paid)
  } </dd> </dl> </div> </div> </div> </div> <div className="bg-background overflow-hidden shadow rounded-lg"> <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"> <span className="text-white text-sm font-medium">⏳</span> </div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 truncate"> Outstanding </dt> <dd className="text-lg font-medium text-foreground"> {
    formatCurrency(summary.total_outstanding)
  } </dd> </dl> </div> </div> </div> </div> <div className="bg-background overflow-hidden shadow rounded-lg"> <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center"> <span className="text-white text-sm font-medium">⚠️</span> </div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 truncate"> Overdue </dt> <dd className="text-lg font-medium text-foreground"> {
    summary.overdue_count
  } </dd> </dl> </div> </div> </div> </div> </div> )
  } {/* Filters */
  } <div className="flex space-x-4">
    <Button onClick={() => setFilter('all')
  } variant={
    filter === 'all' ? 'default' : 'outline'
  } > All Invoices
    </Button>
    <Button onClick={() => setFilter('unpaid')
  } variant={
    filter === 'unpaid' ? 'default' : 'outline'
  } > Unpaid
    </Button>
    <Button onClick={() => setFilter('overdue')
  } variant={
    filter === 'overdue' ? 'default' : 'outline'
  } > Overdue
    </Button> </div> {/* Error Message */
  } {
    error && ( <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded"> {
    error
  } </div> )
  } {/* Invoices Table */
  } <div className="bg-background shadow overflow-hidden sm:rounded-md"> <div className="px-4 py-5 sm:p-6"> <h3 className="text-lg leading-6 font-medium text-foreground mb-4"> Recent Invoices </h3> <div className="overflow-x-auto"> <table className="min-w-full divide-y divide-gray-200"> <thead className="bg-muted"> <tr> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> Invoice </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> Patient </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> Amount </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> Status </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> Due Date </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> Actions </th> </tr> </thead> <tbody className="bg-background divide-y divide-gray-200"> {
    invoices.length === 0 ? ( <tr> <td colSpan={6
  } className="px-6 py-4 text-center text-gray-500"> No invoices found </td> </tr> ) : ( invoices.map((invoice) => ( <tr key={
    invoice.id
  } className="hover:bg-muted"> <td className="px-6 py-4 whitespace-nowrap"> <div className="text-sm font-medium text-foreground"> {
    invoice.invoice_id
  } </div> <div className="text-sm text-gray-500"> {
    formatDate(invoice.invoice_date)
  } </div> </td> <td className="px-6 py-4 whitespace-nowrap"> <div className="text-sm text-foreground"> {
    invoice.patient.user.full_name
  } </div> <div className="text-sm text-gray-500"> {
    invoice.patient.patient_id
  } </div> </td> <td className="px-6 py-4 whitespace-nowrap"> <div className="text-sm font-medium text-foreground"> {
    formatCurrency(invoice.total_amount)
  } </div> <div className="text-sm text-gray-500"> Paid: {
    formatCurrency(invoice.paid_amount)
  } </div> </td> <td className="px-6 py-4 whitespace-nowrap"> <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
    getStatusColor(invoice.status)
  }`
  }> {
    invoice.status.toUpperCase()
  } </span> </td> <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground"> {
    formatDate(invoice.due_date)
  } </td> <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
    <Button size="sm" variant="outline" onClick={() => setSelectedInvoice(invoice)
  } > View
    </Button> {
    invoice.status !== 'paid' && (
    <Button size="sm" onClick={() => handleMarkPaid(invoice.id)
  } className="bg-green-600 hover:bg-green-700 text-white" > Mark Paid
    </Button> )
  } </td> </tr> )) )
  } </tbody> </table> </div> </div> </div> </div> );
  };
  export default BillingDashboard;
