import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import type { RootState, AppDispatch } from '../../store/index';
import { logout } from '../../store/slices/authSlice';
import { toggleSidebar } from '../../store/slices/themeSlice';
import RoleDashboard from './RoleDashboard';
import AnalyticsDashboard from './AnalyticsDashboard';
import PatientList from '../patients/PatientList';
import PatientRegistrationForm from '../patients/PatientRegistrationForm';
import AppointmentCalendar from '../appointments/AppointmentCalendar';
import EmergencyDashboard from '../emergency/EmergencyDashboard';
import EmergencyManagement from '../emergency/EmergencyManagement';
import CommunicationsManagement from '../communications/CommunicationsManagement';
import StaffManagement from '../staff/StaffManagement';
// import InventoryManagement from '../inventory/InventoryManagement'; // TODO: Create this component
import MedicalRecords from '../medical/MedicalRecords';
import PrescriptionManagement from '../medical/PrescriptionManagement';
import LabTestManagement from '../medical/LabTestManagement';
import UserManagement from '../admin/UserManagement';
import BillingManagement from '../billing/BillingManagement';
import StyleDemo from '../demo/StyleDemo';
import StyleGuide from '../demo/StyleGuide';
import MyMedicalRecords from '../patients/MyMedicalHistory';
import MyPrescriptions from '../patients/MyPrescriptions';
import MyLabResults from '../patients/MyLabResults';
import AppointmentBooking from '../patient/AppointmentBooking';
import MyAppointments from '../patients/MyAppointments';
import PatientCare from '../nurses/PatientCare';
import MedicationAdministration from '../nurses/MedicationAdministration';
import MyPatients from '../doctors/MyPatients';
import MedicalNotes from '../doctors/MedicalNotes';
import MySchedule from '../doctors/MySchedule';
import ReportsAnalytics from '../admin/ReportsAnalytics';
import Settings from '../settings/Settings';
import Profile from '../profile/Profile';
import AIServices from '../../pages/AIServices';
import FloatingAIChat from '../ai/FloatingAIChat';
import PermissionUtils from '../../shared/utils/permissions';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const Dashboard: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);
  const { sidebarCollapsed } = useSelector((state: RootState) => state.theme);

  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('dashboard');
  const handleLogout = async () => {
    await dispatch(logout());
    navigate('/login');
  };

  const getNavigationItems = () => {
    const baseItems = [
      { id: 'dashboard', label: t('navigation.dashboard'), icon: '📊' }
    ];

    if (!user) return baseItems;

    // Use the centralized permission system to get navigation items
    const allowedItems = PermissionUtils.getNavigationItems(user);

    // Map allowed items to navigation objects with proper labels and icons
    const navigationMap: Record<string, { id: string; label: string; icon: string }> = {
      'dashboard': { id: 'dashboard', label: t('navigation.dashboard'), icon: '📊' },
      'profile': { id: 'profile', label: 'Profile', icon: '👤' },
      'user-management': { id: 'user-management', label: 'User Management', icon: '👤' },
      'patients': { id: 'patients', label: t('navigation.patients'), icon: '👥' },
      'appointments': { id: 'appointments', label: t('navigation.appointments'), icon: '📅' },
      'medical-records': { id: 'medical-records', label: 'Medical Records', icon: '📋' },
      'emergency': { id: 'emergency', label: t('navigation.emergency'), icon: '🚨' },
      'staff': { id: 'staff', label: t('navigation.staff'), icon: '👨‍⚕️' },
      'inventory': { id: 'inventory', label: t('navigation.inventory'), icon: '📦' },
      'billing': { id: 'billing', label: t('navigation.billing'), icon: '💰' },
      'reports': { id: 'reports', label: t('navigation.reports'), icon: '📈' },
      'ai-services': { id: 'ai-services', label: 'AI Services', icon: '🤖' },
      'settings': { id: 'settings', label: 'Settings', icon: '⚙️' },
      // Role-specific items
      'my-patients': { id: 'my-patients', label: 'My Patients', icon: '👥' },
      'prescriptions': { id: 'prescriptions', label: 'Prescriptions', icon: '💊' },
      'lab-tests': { id: 'lab-tests', label: 'Lab Tests', icon: '🧪' },
      'medical-notes': { id: 'medical-notes', label: 'Medical Notes', icon: '📝' },
      'my-schedule': { id: 'my-schedule', label: 'My Schedule', icon: '📅' },
      'patient-care': { id: 'patient-care', label: 'Patient Care', icon: '👥' },
      'medications': { id: 'medications', label: 'Medications', icon: '💊' },
      'my-appointments': { id: 'my-appointments', label: 'My Appointments', icon: '📅' },
      'my-medical-records': { id: 'my-medical-records', label: 'My Medical Records', icon: '📋' },
      'my-billing': { id: 'my-billing', label: 'My Billing', icon: '💰' },
    };

    // Add development items for admin users
    if (PermissionUtils.hasAdminAccess(user)) {
      navigationMap['style-demo'] = { id: 'style-demo', label: 'Style Demo', icon: '🎨' };
      navigationMap['style-guide'] = { id: 'style-guide', label: 'Style Guide', icon: '📋' };
    }

    return allowedItems
      .map(itemId => navigationMap[itemId])
      .filter(Boolean); // Remove undefined items
  };
  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <RoleDashboard />;
      case 'ai-services':
        return <AIServices />;
      case 'analytics':
        return <AnalyticsDashboard />;
      case 'patients':
        return <PatientList />;
      case 'register-patient':
        return <PatientRegistrationForm />;
      case 'appointments':
        return <AppointmentCalendar />;
      case 'emergency':
        return <EmergencyDashboard />;
      case 'emergency-management':
        return <EmergencyManagement />;
      case 'communications':
        return <CommunicationsManagement />;
      case 'staff':
        return <StaffManagement />;
      case 'inventory':
        return <div className="p-6 text-center">
          <h3 className="text-lg font-semibold mb-2">Inventory Management</h3>
          <p className="text-muted-foreground">Coming Soon</p>
        </div>;
      case 'medical-records':
        return <MedicalRecords />;
      case 'prescriptions':
        return <PrescriptionManagement />;
      case 'lab-tests':
        return <LabTestManagement />;
      case 'user-management':
        return <UserManagement />;
      case 'billing':
        return <BillingManagement />;
      case 'style-demo':
        return <StyleDemo />;
      case 'style-guide':
        return <StyleGuide />;
      case 'my-medical-records':
        return <MyMedicalRecords />;
      case 'my-prescriptions':
        return <MyPrescriptions />;
      case 'my-lab-results':
        return <MyLabResults />;
      case 'my-appointments':
        return <MyAppointments />;
      case 'appointment-booking':
        return <AppointmentBooking />;
      case 'my-patients':
        return <MyPatients />;
      case 'medical-notes':
        return <MedicalNotes />;
      case 'my-schedule':
        return <MySchedule />;
      case 'patient-care':
        return <PatientCare />;
      case 'medications':
        return <MedicationAdministration />;
      case 'reports':
        return <ReportsAnalytics />;
      case 'settings':
        return <Settings />;
      case 'profile':
        return <Profile />;
      default:
        return (
          <div className="text-center macos-text-secondary py-8">
            {t('ui.featureComingSoon')}
          </div>
        );
    }
  };
  const navigationItems = getNavigationItems();

  return (
    <div className="min-h-screen bg-background flex">
      {/* Collapsible Sidebar */}
      <div className={`sidebar-container ${sidebarCollapsed ? 'collapsed' : 'expanded'}`}>
        <div className="sidebar">
          {/* Sidebar Toggle Button */}
          <button
            onClick={() => dispatch(toggleSidebar())}
            className="sidebar-toggle"
            title={sidebarCollapsed ? t('sidebar.expand') : t('sidebar.collapse')}
          >
            {sidebarCollapsed ? <ChevronRight className="w-3 h-3" /> : <ChevronLeft className="w-3 h-3" />}
          </button>
          {/* Header */}
          <div className="sidebar-header">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-primary rounded-xl flex items-center justify-center shadow-glass flex-shrink-0">
                <span className="text-primary-foreground font-bold text-lg">🏥</span>
              </div>
              <h1 className="text-xl font-bold text-primary">
                {t('dashboard.title')}
              </h1>
            </div>
            <div className="user-info glass-light rounded-lg p-3">
              <p className="text-sm font-semibold text-foreground">{user?.full_name}</p>
              <p className="text-xs text-muted-foreground capitalize font-medium">{user?.role}</p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="sidebar-nav">
            {navigationItems.map((item, index) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`sidebar-nav-item ${
                  activeTab === item.id ? 'active' : ''
                }`}
                style={{ animationDelay: `${index * 50}ms` }}
                title={sidebarCollapsed ? item.label : undefined}
              >
                <span className="sidebar-nav-icon">
                  {item.icon}
                </span>
                <span className="sidebar-nav-text">
                  {item.label}
                </span>
                {sidebarCollapsed && (
                  <div className="sidebar-tooltip">
                    {item.label}
                  </div>
                )}
              </button>
            ))}
          </nav>

          {/* Bottom Section */}
          <div className="border-t border-border/30 p-2 space-y-1">
            <button
              onClick={() => setActiveTab('profile')}
              className={`sidebar-nav-item ${
                activeTab === 'profile' ? 'active' : ''
              }`}
              title={sidebarCollapsed ? `${user?.first_name} ${user?.last_name}` : undefined}
            >
              <div className="sidebar-nav-icon w-8 h-8 bg-primary text-primary-foreground rounded-lg flex items-center justify-center shadow-glass">
                <span className="text-xs font-bold">
                  {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}
                </span>
              </div>
              <div className="sidebar-nav-text min-w-0">
                <p className="text-sm font-semibold truncate">
                  {user?.first_name} {user?.last_name}
                </p>
                <p className="text-xs opacity-75 capitalize truncate">
                  {user?.role}
                </p>
              </div>
              {sidebarCollapsed && (
                <div className="sidebar-tooltip">
                  {user?.first_name} {user?.last_name}
                </div>
              )}
            </button>

            <button
              onClick={() => setActiveTab('settings')}
              className={`sidebar-nav-item ${
                activeTab === 'settings' ? 'active' : ''
              }`}
              title={sidebarCollapsed ? t('common.settings') : undefined}
            >
              <span className="sidebar-nav-icon">⚙️</span>
              <span className="sidebar-nav-text">{t('common.settings')}</span>
              {sidebarCollapsed && (
                <div className="sidebar-tooltip">
                  {t('common.settings')}
                </div>
              )}
            </button>

            <button
              onClick={handleLogout}
              className="sidebar-nav-item hover:bg-destructive/10 hover:text-destructive"
              title={sidebarCollapsed ? t('auth.logout') : undefined}
            >
              <span className="sidebar-nav-icon">🚪</span>
              <span className="sidebar-nav-text">{t('auth.logout')}</span>
              {sidebarCollapsed && (
                <div className="sidebar-tooltip">
                  {t('auth.logout')}
                </div>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-screen overflow-hidden">
        {/* Page Content */}
        <main className="flex-1 p-6 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {renderContent()}
          </div>
        </main>
      </div>

      {/* Floating AI Chat */}
      <FloatingAIChat />
    </div>
  );
};

export default Dashboard;
