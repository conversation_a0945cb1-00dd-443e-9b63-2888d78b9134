/**
 * ReceptionistDashboard Component
 * Uses shared components for consistent layout and functionality
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Calendar,
  Users,
  Phone,
  Clock,
  UserPlus,
  CreditCard,
  CheckCircle,
  AlertCircle,
  FileText,
  CalendarPlus,
  UserCheck
} from 'lucide-react';

// Shared components
import { DashboardSection, QuickActions } from '../../shared/components/layouts/DashboardLayout';
import { MetricGrid } from '../../shared/components/data-display/MetricCard';
import { Badge } from '../ui/badge';
import { getStatusClass, getPriorityClass } from '../../utils/styleUtils';

const ReceptionistDashboard: React.FC = () => {
  const { t } = useTranslation();

  // Receptionist-specific metrics
  const receptionistMetrics = [
    {
      id: 'appointments-today',
      label: t('receptionist.appointmentsToday'),
      value: '24',
      icon: Calendar,
      color: 'feature-blue',
      change: '+3',
      trend: 'up' as const,
      description: 'Scheduled for today',
    },
    {
      id: 'new-registrations',
      label: t('receptionist.newRegistrations'),
      value: '6',
      icon: UserPlus,
      color: 'feature-green',
      change: '+2',
      trend: 'up' as const,
      description: 'Today',
    },
    {
      id: 'pending-payments',
      label: t('receptionist.pendingPayments'),
      value: '8',
      icon: CreditCard,
      color: 'feature-orange',
      description: 'Awaiting processing',
    },
    {
      id: 'phone-calls',
      label: t('receptionist.phoneCalls'),
      value: '15',
      icon: Phone,
      color: 'feature-purple',
      change: '+5',
      trend: 'up' as const,
      description: 'Today',
    },
  ];

  // Quick actions for receptionists
  const quickActions = [
    {
      id: 'register-patient',
      title: t('receptionist.registerPatient'),
      description: t('receptionist.addNewPatientToSystem'),
      icon: UserPlus,
      onClick: () => console.log('Register patient'),
    },
    {
      id: 'schedule-appointment',
      title: t('receptionist.scheduleAppointment'),
      description: t('receptionist.bookNewAppointment'),
      icon: CalendarPlus,
      onClick: () => console.log('Schedule appointment'),
    },
    {
      id: 'process-payment',
      title: t('receptionist.processPayment'),
      description: t('receptionist.handleBillingAndPayments'),
      icon: CreditCard,
      onClick: () => console.log('Process payment'),
    },
    {
      id: 'patient-records',
      title: t('receptionist.patientRecords'),
      description: t('receptionist.accessPatientFiles'),
      icon: FileText,
      onClick: () => console.log('Patient records'),
    },
  ];

  // Upcoming appointments data
  const upcomingAppointments = [
    {
      id: 1,
      time: '09:00 AM',
      patient: 'Sarah Johnson',
      doctor: 'Dr. Smith',
      type: 'Consultation',
      status: 'confirmed'
    },
    {
      id: 2,
      time: '09:30 AM',
      patient: 'Michael Brown',
      doctor: 'Dr. Davis',
      type: 'Follow-up',
      status: 'pending'
    },
    {
      id: 3,
      time: '10:00 AM',
      patient: 'Emily Wilson',
      doctor: 'Dr. Johnson',
      type: 'Check-up',
      status: 'confirmed'
    },
    {
      id: 4,
      time: '10:30 AM',
      patient: 'Robert Miller',
      doctor: 'Dr. Brown',
      type: 'Emergency',
      status: 'urgent'
    }
  ];

  // Pending tasks data
  const pendingTasks = [
    {
      id: 1,
      task: 'Verify insurance for John Doe',
      priority: 'high',
      time: '30 min ago'
    },
    {
      id: 2,
      task: 'Schedule follow-up for Mary Smith',
      priority: 'medium',
      time: '1 hour ago'
    },
    {
      id: 3,
      task: 'Process payment for Tom Wilson',
      priority: 'low',
      time: '2 hours ago'
    }
  ];

  // Using unified style system - no need for custom color functions

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-violet-600 flex items-center justify-center shadow-lg">
            <UserCheck className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('receptionist.title')}
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              {t('receptionist.welcome')}
            </p>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className="flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                {t('receptionist.onDuty')}
              </Badge>
            </div>
          </div>
        </div>
      </div>
      {/* Today's Statistics */}
      <DashboardSection title="Today's Overview">
        <MetricGrid metrics={receptionistMetrics} columns={4} />
      </DashboardSection>

      {/* Quick Actions */}
      <DashboardSection
        title={t('dashboard.quickActions')}
        description={t('receptionist.quickActionsDescription')}
        variant="glass"
      >
        <QuickActions actions={quickActions} columns={4} />
      </DashboardSection>

      {/* Appointments and Tasks */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Appointments */}
        <DashboardSection
          title={t('receptionist.upcomingAppointments')}
          variant="glass"
        >
          <div className="space-y-4">
            {upcomingAppointments.map((appointment) => (
              <div key={appointment.id} className="flex items-center justify-between p-4 glass rounded-lg hover:glass-hover transition-all duration-200">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <p className="font-medium macos-text-primary">
                      {appointment.time} - {appointment.patient}
                    </p>
                    <Badge className={getStatusClass(appointment.status)}>
                      {appointment.status}
                    </Badge>
                  </div>
                  <p className="text-sm macos-text-secondary">
                    {appointment.doctor} • {appointment.type}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </DashboardSection>

        {/* Pending Tasks */}
        <DashboardSection
          title="Pending Tasks"
          variant="glass"
        >
          <div className="space-y-4">
            {pendingTasks.map((task) => (
              <div key={task.id} className="flex items-center justify-between p-4 glass rounded-lg hover:glass-hover transition-all duration-200">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <p className="font-medium macos-text-primary">
                      {task.task}
                    </p>
                    <Badge className={getPriorityClass(task.priority)}>
                      {task.priority}
                    </Badge>
                  </div>
                  <p className="text-sm macos-text-tertiary">
                    {task.time}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </DashboardSection>
      </div>

      {/* Today's Schedule Overview */}
      <DashboardSection
        title={t('receptionist.todayScheduleOverview')}
        variant="glass"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 glass rounded-lg">
            <p className="text-3xl font-bold macos-accent-text">24</p>
            <p className="text-sm macos-text-secondary">{t('receptionist.totalAppointments')}</p>
          </div>
          <div className="text-center p-4 glass rounded-lg">
            <p className="text-3xl font-bold status-success">18</p>
            <p className="text-sm macos-text-secondary">{t('receptionist.confirmed')}</p>
          </div>
          <div className="text-center p-4 glass rounded-lg">
            <p className="text-3xl font-bold status-warning">6</p>
            <p className="text-sm macos-text-secondary">{t('receptionist.pending')}</p>
          </div>
        </div>
      </DashboardSection>
    </div>
  );
};

export default ReceptionistDashboard;
