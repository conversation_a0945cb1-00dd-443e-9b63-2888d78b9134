import React from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store/index';
import AdminDashboard from './AdminDashboard';
import DoctorDashboard from './DoctorDashboard';
import PatientDashboard from './PatientDashboard';
import NurseDashboard from './NurseDashboard';
import ReceptionistDashboard from './ReceptionistDashboard';
import { AlertCircle, Loader2 } from 'lucide-react';
import { USER_ROLES } from '../../shared/utils/constants';
import PermissionUtils from '../../shared/utils/permissions';

const RoleDashboard: React.FC = () => {
  const { user, loading } = useSelector((state: RootState) => state.auth);

  // Show loading state while authentication is being checked
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-3">
          <Loader2 className="w-6 h-6 animate-spin text-primary" />
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // If no user is logged in, show a message
  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="w-full max-w-md bg-background rounded-lg shadow-lg p-6">
          <div className="flex items-center text-amber-700 dark:text-amber-400 mb-4">
            <AlertCircle className="w-5 h-5 mr-2" />
            <h3 className="text-lg font-semibold">Authentication Required</h3>
          </div>
          <p className="text-muted-foreground">
            Please log in to access your dashboard.
          </p>
        </div>
      </div>
    );
  }

  // Validate user role and route to appropriate dashboard
  const roleDisplayName = PermissionUtils.getRoleDisplayName(user.role);

  switch (user.role) {
    case USER_ROLES.ADMIN:
      return <AdminDashboard />;
    case USER_ROLES.DOCTOR:
      return <DoctorDashboard />;
    case USER_ROLES.PATIENT:
      return <PatientDashboard />;
    case USER_ROLES.NURSE:
      return <NurseDashboard />;
    case USER_ROLES.RECEPTIONIST:
      return <ReceptionistDashboard />;
    default:
      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="w-full max-w-md bg-background rounded-lg shadow-lg p-6">
            <div className="flex items-center text-rose-700 dark:text-rose-400 mb-4">
              <AlertCircle className="w-5 h-5 mr-2" />
              <h3 className="text-lg font-semibold">Unknown Role</h3>
            </div>
            <p className="text-muted-foreground mb-4">
              Your user role "{user.role}" is not recognized.
            </p>
            <p className="text-sm text-muted-foreground">
              Expected roles: {Object.values(USER_ROLES).join(', ')}
            </p>
            <p className="text-sm text-muted-foreground mt-2">
              Please contact your administrator for assistance.
            </p>
          </div>
        </div>
      );
  }
};

export default RoleDashboard;
