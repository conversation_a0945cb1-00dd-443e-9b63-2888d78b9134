import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/Button';
import { getTriageClass, getStatusClass } from '../../utils/styleUtils';

interface EmergencyCase {
  id: number;
  case_id: string;
  patient_name: string;
  arrival_time: string;
  triage_level: string;
  chief_complaint: string;
  status: string;
  attending_doctor: string;
  bed_number?: string;
}

interface TriageStats {
  level_1: number;
  level_2: number;
  level_3: number;
  level_4: number;
  level_5: number;
}

const EmergencyDashboard: React.FC = () => {
  const { t } = useTranslation();
  const [emergencyCases, setEmergencyCases] = useState<EmergencyCase[]>([]);
  const [triageStats, setTriageStats] = useState<TriageStats>({
    level_1: 0,
    level_2: 0,
    level_3: 0,
    level_4: 0,
    level_5: 0,
  }); // Mock data
  useEffect(() => {
    const mockCases: EmergencyCase[] = [ {
    id: 1, case_id: 'ER000001', patient_name: '<PERSON>', arrival_time: '2024-01-15T14:30:00Z', triage_level: 'level_1', chief_complaint: 'Chest pain, difficulty breathing', status: 'active', attending_doctor: 'Dr. Smith', bed_number: 'ER-1',
  }, {
    id: 2, case_id: 'ER000002', patient_name: 'Jane Wilson', arrival_time: '2024-01-15T15:15:00Z', triage_level: 'level_2', chief_complaint: 'Severe abdominal pain', status: 'active', attending_doctor: 'Dr. Johnson', bed_number: 'ER-3',
  }, {
    id: 3, case_id: 'ER000003', patient_name: 'Robert Brown', arrival_time: '2024-01-15T15:45:00Z', triage_level: 'level_3', chief_complaint: 'Laceration on hand', status: 'waiting', attending_doctor: 'Dr. Davis',
  }, ];
  setEmergencyCases(mockCases); // Calculate triage stats

const stats = mockCases.reduce((acc, case_) => {
    acc[case_.triage_level as keyof TriageStats]++;
  return acc;
  }, {
    level_1: 0, level_2: 0, level_3: 0, level_4: 0, level_5: 0
  });
  setTriageStats(stats);
  }, []);
  const getTriageLabel = (level: string) => {
    switch (level) {
    case 'level_1': return t('emergency.immediate');
  case 'level_2': return t('emergency.urgent');
  case 'level_3': return t('emergency.lessUrgent');
  case 'level_4': return t('emergency.nonUrgent');
  case 'level_5': return t('emergency.clinicCare');
  default: return 'Unknown';
  }
  };
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Emergency Alert Banner */
  } <div className="bg-red-600 text-white p-4 rounded-lg"> <div className="flex items-center justify-between"> <div className="flex items-center space-x-3"> <div className="animate-pulse"> <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20"> <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" /> </svg> </div> <div> <h3 className="font-semibold">{
    t('emergency.status')
  }</h3> <p className="text-sm">{
    t('emergency.currentlyManaging')
  } {
    emergencyCases.length
  } {
    t('emergency.activeCases')
  }</p> </div> </div>
    <Button variant="outline" className="text-white border-white hover:bg-background hover:text-rose-700 dark:text-rose-400"> {
    t('emergency.codeBlueAlert')
  }
    </Button> </div> </div> {/* Triage Overview */
  } <div className="grid grid-cols-1 md:grid-cols-5 gap-4"> {[ {
    level: 'level_1', label: t('emergency.immediate'), color: 'red', count: triageStats.level_1
  }, {
    level: 'level_2', label: t('emergency.urgent'), color: 'orange', count: triageStats.level_2
  }, {
    level: 'level_3', label: t('emergency.lessUrgent'), color: 'yellow', count: triageStats.level_3
  }, {
    level: 'level_4', label: t('emergency.nonUrgent'), color: 'green', count: triageStats.level_4
  }, {
    level: 'level_5', label: t('emergency.clinicCare'), color: 'blue', count: triageStats.level_5
  }, ].map((triage) => ( <div key={
    triage.level
  } className="glass-card"> <div className="flex items-center"> <div className="p-3 rounded-lg bg-primary/10"> <div className="w-6 h-6 rounded-full bg-primary"
  }></div> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">{
    triage.label
  }</p> <p className="text-2xl font-bold text-foreground">{
    triage.count
  }</p> </div> </div> </div> ))
  } </div> {/* Quick Actions */
  } <div className="glass-card"> <h3 className="text-lg font-semibold text-foreground mb-4">{
    t('emergency.quickActions')
  }</h3> <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
    <Button className="h-20 flex flex-col items-center justify-center"> <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M12 6v6m0 0v6m0-6h6m-6 0H6" /> </svg> {
    t('emergency.newPatient')
  }
    </Button>
    <Button variant="outline" className="h-20 flex flex-col items-center justify-center"> <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" /> </svg> {
    t('emergency.triage')
  }
    </Button>
    <Button variant="outline" className="h-20 flex flex-col items-center justify-center"> <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z" /> </svg> {
    t('emergency.labOrders')
  }
    </Button>
    <Button variant="destructive" className="h-20 flex flex-col items-center justify-center"> <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" /> </svg> {
    t('emergency.emergencyAlert')
  }
    </Button> </div> </div> {/* Active Cases */
  } <div className="glass rounded-lg"> <div className="p-6 border-b border-border"> <div className="flex items-center justify-between"> <h3 className="text-lg font-semibold text-foreground">{
    t('emergency.activeCasesTitle')
  }</h3>
    <Button variant="outline" size="sm"> {
    t('ui.refresh')
  }
    </Button> </div> </div> <div className="overflow-x-auto"> <table className="min-w-full divide-y divide-gray-200"> <thead className="bg-muted"> <tr> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> {
    t('emergency.caseId')
  } </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> {
    t('emergency.patient')
  } </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> {
    t('emergency.triage')
  } </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> {
    t('emergency.chiefComplaint')
  } </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> {
    t('emergency.doctor')
  } </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> {
    t('common.status')
  } </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> {
    t('common.actions')
  } </th> </tr> </thead> <tbody className="bg-background divide-y divide-gray-200"> {
    emergencyCases.map((case_) => ( <tr key={
    case_.id
  } className="hover:bg-muted"> <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-sky-700 dark:text-sky-400"> {
    case_.case_id
  } </td> <td className="px-6 py-4 whitespace-nowrap"> <div> <div className="text-sm font-medium text-foreground">{
    case_.patient_name
  }</div> <div className="text-sm text-gray-500"> {
    t('emergency.arrived')
  }: {
    new Date(case_.arrival_time).toLocaleTimeString()
  } </div> </div> </td> <td className="px-6 py-4 whitespace-nowrap"> <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
    getTriageClass(case_.triage_level)
  }`
  }> {
    getTriageLabel(case_.triage_level)
  } </span> </td> <td className="px-6 py-4 text-sm text-foreground max-w-xs truncate"> {
    case_.chief_complaint
  } </td> <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground"> {
    case_.attending_doctor
  } </td> <td className="px-6 py-4 whitespace-nowrap"> <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
    getStatusClass(case_.status)
  }`
  }> {
    case_.status
  } </span> </td> <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
    <Button size="sm" variant="outline"> {
    t('common.view')
  }
    </Button>
    <Button size="sm"> {
    t('emergency.update')
  }
    </Button> </td> </tr> ))
  } </tbody> </table> </div> </div> {/* Bed Status */
  } <div className="glass-card"> <h3 className="text-lg font-semibold text-foreground mb-4">{
    t('emergency.bedStatus')
  }</h3> <div className="grid grid-cols-2 md:grid-cols-6 gap-4"> {
    Array.from({
    length: 12
  }, (_, i) => i + 1).map((bedNum) => {
    const isOccupied = bedNum <= 3;
  return ( <div key={
    bedNum
  } className={`p-4 rounded-lg border-2 text-center ${
    isOccupied ? 'border-destructive/20 bg-destructive/10' : 'border-primary/20 bg-primary/10'
  }`
  } > <div className="text-sm font-medium">ER-{
    bedNum
  }</div> <div className={`text-xs ${
    isOccupied ? 'text-rose-700 dark:text-rose-400' : 'text-emerald-700 dark:text-emerald-400'
  }`
  }> {
    isOccupied ? t('emergency.occupied') : t('emergency.available')
  } </div> </div> );
  })
  } </div> </div> </div> </div> );
  };
  export default EmergencyDashboard;
