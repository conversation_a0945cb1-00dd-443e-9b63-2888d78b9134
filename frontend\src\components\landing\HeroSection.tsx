import React from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    Button
  } from '../ui/Button';
  import {
    ArrowRight, Brain, Shield, Zap, Sparkles, Bot, Activity
  } from 'lucide-react';
  const HeroSection: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  return ( <section className="relative bg-gradient-to-br from-background via-background to-secondary py-12 sm:py-16 lg:py-20 overflow-hidden min-h-screen flex items-center"> {/* Enhanced AI Medical Network Background */
  } <div className="absolute inset-0 opacity-40"> <svg className="absolute inset-0 w-full h-full" viewBox="0 0 1400 900" preserveAspectRatio="xMidYMid slice"> <defs> {/* Enhanced Gradients */
  } <linearGradient id="primaryGlow" x1="0%" y1="0%" x2="100%" y2="100%"> <stop offset="0%" stopColor="#06b6d4" stopOpacity="0.9"/> <stop offset="30%" stopColor="#3b82f6" stopOpacity="0.8"/> <stop offset="70%" stopColor="#8b5cf6" stopOpacity="0.7"/> <stop offset="100%" stopColor="#10b981" stopOpacity="0.6"/> </linearGradient> <radialGradient id="centralCore" cx="50%" cy="50%" r="60%"> <stop offset="0%" stopColor="#10b981" stopOpacity="0.9"/> <stop offset="40%" stopColor="#06b6d4" stopOpacity="0.7"/> <stop offset="80%" stopColor="#3b82f6" stopOpacity="0.5"/> <stop offset="100%" stopColor="#1e293b" stopOpacity="0.2"/> </radialGradient> <linearGradient id="networkFlow" x1="0%" y1="0%" x2="100%" y2="0%"> <stop offset="0%" stopColor="#06b6d4" stopOpacity="0"/> <stop offset="50%" stopColor="#06b6d4" stopOpacity="0.8"/> <stop offset="100%" stopColor="#06b6d4" stopOpacity="0"/> </linearGradient> {/* Fixed Filters */
  } <filter id="softGlow" x="-100%" y="-100%" width="300%" height="300%"> <feGaussianBlur stdDeviation="2" result="coloredBlur"/> <feMerge> <feMergeNode in="coloredBlur"/> <feMergeNode in="SourceGraphic"/> </feMerge> </filter> <filter id="strongGlow" x="-100%" y="-100%" width="300%" height="300%"> <feGaussianBlur stdDeviation="3" result="coloredBlur"/> <feMerge> <feMergeNode in="coloredBlur"/> <feMergeNode in="SourceGraphic"/> </feMerge> </filter> {/* Patterns */
  } <pattern id="techGrid" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse"> <path d="M 60 0 L 0 0 0 60" fill="none" stroke="#06b6d4" strokeWidth="0.8" opacity="0.6"/> <circle cx="0" cy="0" r="1.5" fill="#06b6d4" opacity="0.7"/> <circle cx="60" cy="60" r="1.5" fill="#3b82f6" opacity="0.6"/> <circle cx="30" cy="30" r="1" fill="#8b5cf6" opacity="0.5"/> </pattern> </defs> {/* Tech Grid Background */
  } <rect width="100%" height="100%" fill="url(#techGrid)" opacity="0.8"/> {/* Central AI Network Hub */
  } <circle cx="700" cy="450" r="80" fill="url(#centralCore)" filter="url(#strongGlow)"> <animate attributeName="r" values="80;100;80" dur="6s" repeatCount="indefinite"/> </circle> {/* Neural Network Connections */
  } <g stroke="url(#primaryGlow)" strokeWidth="3" fill="none" filter="url(#softGlow)"> {/* Main Network Pathways */
  } <path d="M 100 150 Q 350 100 700 150 Q 1050 100 1300 150" opacity="0.9"> <animate attributeName="stroke-dasharray" values="0,1200;1200,0" dur="10s" repeatCount="indefinite"/> </path> <path d="M 100 750 Q 350 800 700 750 Q 1050 800 1300 750" opacity="0.9"> <animate attributeName="stroke-dasharray" values="1200,0;0,1200" dur="10s" repeatCount="indefinite"/> </path> <path d="M 50 450 Q 200 200 700 450 Q 1200 700 1350 450" opacity="0.8"> <animate attributeName="stroke-dasharray" values="0,1300;1300,0" dur="12s" repeatCount="indefinite"/> </path> {/* Vertical Connections */
  } <path d="M 300 50 Q 350 450 300 850" opacity="0.7" strokeWidth="2.5"> <animate attributeName="stroke-dasharray" values="0,800;800,0" dur="8s" repeatCount="indefinite"/> </path> <path d="M 1100 50 Q 1050 450 1100 850" opacity="0.7" strokeWidth="2.5"> <animate attributeName="stroke-dasharray" values="800,0;0,800" dur="8s" repeatCount="indefinite"/> </path> </g> {/* AI Processing Nodes */
  } <g opacity="1"> {/* Corner Nodes */
  } <circle cx="100" cy="150" r="30" fill="url(#primaryGlow)" filter="url(#softGlow)"> <animate attributeName="r" values="30;40;30" dur="4s" repeatCount="indefinite"/> </circle> <circle cx="1300" cy="150" r="30" fill="url(#primaryGlow)" filter="url(#softGlow)"> <animate attributeName="r" values="30;40;30" dur="4s" begin="1s" repeatCount="indefinite"/> </circle> <circle cx="100" cy="750" r="30" fill="url(#primaryGlow)" filter="url(#softGlow)"> <animate attributeName="r" values="30;40;30" dur="4s" begin="2s" repeatCount="indefinite"/> </circle> <circle cx="1300" cy="750" r="30" fill="url(#primaryGlow)" filter="url(#softGlow)"> <animate attributeName="r" values="30;40;30" dur="4s" begin="3s" repeatCount="indefinite"/> </circle> {/* Medical AI Stations */
  } <circle cx="300" cy="150" r="22" fill="#10b981" opacity="0.9" filter="url(#softGlow)"> <animate attributeName="r" values="22;32;22" dur="5s" repeatCount="indefinite"/> </circle> <circle cx="1100" cy="150" r="22" fill="#10b981" opacity="0.9" filter="url(#softGlow)"> <animate attributeName="r" values="22;32;22" dur="5s" begin="1.5s" repeatCount="indefinite"/> </circle> <circle cx="300" cy="750" r="22" fill="#10b981" opacity="0.9" filter="url(#softGlow)"> <animate attributeName="r" values="22;32;22" dur="5s" begin="3s" repeatCount="indefinite"/> </circle> <circle cx="1100" cy="750" r="22" fill="#10b981" opacity="0.9" filter="url(#softGlow)"> <animate attributeName="r" values="22;32;22" dur="5s" begin="4.5s" repeatCount="indefinite"/> </circle> </g> {/* Data Flow Particles */
  } <g opacity="1"> {/* Horizontal Flow */
  } <circle cx="0" cy="150" r="5" fill="#06b6d4" filter="url(#softGlow)"> <animateTransform attributeName="transform" type="translate" values="0,0;1400,0;0,0" dur="15s" repeatCount="indefinite"/> </circle> <circle cx="0" cy="450" r="5" fill="#3b82f6" filter="url(#softGlow)"> <animateTransform attributeName="transform" type="translate" values="0,0;1400,0;0,0" dur="18s" repeatCount="indefinite"/> </circle> <circle cx="0" cy="750" r="5" fill="#8b5cf6" filter="url(#softGlow)"> <animateTransform attributeName="transform" type="translate" values="0,0;1400,0;0,0" dur="20s" repeatCount="indefinite"/> </circle> {/* Vertical Flow */
  } <circle cx="300" cy="0" r="5" fill="#10b981" filter="url(#softGlow)"> <animateTransform attributeName="transform" type="translate" values="0,0;0,900;0,0" dur="12s" repeatCount="indefinite"/> </circle> <circle cx="700" cy="0" r="5" fill="#06b6d4" filter="url(#softGlow)"> <animateTransform attributeName="transform" type="translate" values="0,0;0,900;0,0" dur="16s" repeatCount="indefinite"/> </circle> <circle cx="1100" cy="0" r="5" fill="#3b82f6" filter="url(#softGlow)"> <animateTransform attributeName="transform" type="translate" values="0,0;0,900;0,0" dur="14s" repeatCount="indefinite"/> </circle> </g> {/* Floating Medical Icons */
  } <g fill="#10b981" opacity="0.9" filter="url(#softGlow)"> {/* Medical Cross 1 */
  } <g transform="translate(200,300)"> <rect x="-15" y="-5" width="30" height="10" rx="3"/> <rect x="-5" y="-15" width="10" height="30" rx="3"/> <animateTransform attributeName="transform" type="rotate" values="0 200 300;360 200 300" dur="30s" repeatCount="indefinite"/> </g> {/* Medical Cross 2 */
  } <g transform="translate(1200,600)"> <rect x="-15" y="-5" width="30" height="10" rx="3"/> <rect x="-5" y="-15" width="10" height="30" rx="3"/> <animateTransform attributeName="transform" type="rotate" values="360 1200 600;0 1200 600" dur="25s" repeatCount="indefinite"/> </g> {/* DNA Helix Representation */
  } <g transform="translate(500,200)" fill="#06b6d4" opacity="0.8"> <ellipse cx="0" cy="0" rx="12" ry="4" opacity="0.8"> <animateTransform attributeName="transform" type="rotate" values="0;360" dur="8s" repeatCount="indefinite"/> </ellipse> <ellipse cx="0" cy="0" rx="12" ry="4" opacity="0.8"> <animateTransform attributeName="transform" type="rotate" values="180;540" dur="8s" repeatCount="indefinite"/> </ellipse> </g> <g transform="translate(900,700)" fill="#8b5cf6" opacity="0.8"> <ellipse cx="0" cy="0" rx="12" ry="4" opacity="0.8"> <animateTransform attributeName="transform" type="rotate" values="0;360" dur="10s" repeatCount="indefinite"/> </ellipse> <ellipse cx="0" cy="0" rx="12" ry="4" opacity="0.8"> <animateTransform attributeName="transform" type="rotate" values="180;540" dur="10s" repeatCount="indefinite"/> </ellipse> </g> </g> </svg> </div> {/* Single Grid Layout - Text Overlaid on SVG Background */
  } <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10"> <div className="text-center max-w-5xl mx-auto"> {/* AI Status Badge */
  } <div className="flex items-center justify-center mb-6 lg:mb-8"> <div className="flex items-center space-x-2 lg:space-x-3 glass border border-primary/40 px-4 lg:px-6 py-2 lg:py-3 rounded-full shadow-2xl hover:shadow-primary/30 transition-all duration-300 hover:scale-105">
    <Bot className="w-5 h-5 lg:w-6 lg:h-6 text-primary drop-shadow-lg" /> <span className="text-primary font-bold text-sm lg:text-base tracking-wide drop-shadow-lg">{
    t('landing.aiPowered')
  }</span> <div className="w-2 h-2 bg-primary rounded-full animate-pulse shadow-lg shadow-primary/50"></div> </div> </div> {/* Main Title */
  } <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold mb-4 lg:mb-6 leading-tight tracking-tight"> <span className="text-foreground block mb-1 lg:mb-2 drop-shadow-2xl">{
    t('landing.heroTitle')
  }</span> <span className="gradient-text-enhanced block drop-shadow-2xl">{
    t('landing.heroTitleHighlight')
  }</span> </h1> {/* Description */
  } <p className="text-base sm:text-lg lg:text-xl text-muted-foreground mb-6 lg:mb-8 leading-relaxed max-w-3xl mx-auto font-light tracking-wide drop-shadow-xl"> {
    t('landing.heroDescription')
  } </p> {/* AI Capabilities */
  } <div className="flex flex-wrap gap-3 lg:gap-4 mb-8 lg:mb-12 justify-center"> <div className="flex items-center space-x-2 lg:space-x-3 glass border border-primary/40 px-4 lg:px-6 py-2 lg:py-3 rounded-full shadow-xl hover:shadow-primary/30 transition-all duration-300 hover:scale-105">
    <Brain className="w-4 h-4 lg:w-5 lg:h-5 text-primary drop-shadow-lg" /> <span className="text-primary font-semibold text-sm lg:text-base tracking-wide drop-shadow-lg">{
    t('landing.features.aiDiagnostics.title')
  }</span> </div> <div className="flex items-center space-x-2 lg:space-x-3 bg-gradient-to-r from-purple-500/25 to-violet-500/25 backdrop-blur-xl border border-purple-500/40 px-4 lg:px-6 py-2 lg:py-3 rounded-full shadow-xl shadow-purple-500/20 hover:shadow-purple-500/30 transition-all duration-300 hover:scale-105">
    <Shield className="w-4 h-4 lg:w-5 lg:h-5 text-purple-400 drop-shadow-lg" /> <span className="text-purple-200 font-semibold text-sm lg:text-base tracking-wide drop-shadow-lg">{
    t('landing.secureData')
  }</span> </div> <div className="flex items-center space-x-2 lg:space-x-3 bg-gradient-to-r from-emerald-500/25 to-green-500/25 backdrop-blur-xl border border-emerald-500/40 px-4 lg:px-6 py-2 lg:py-3 rounded-full shadow-xl shadow-emerald-500/20 hover:shadow-emerald-500/30 transition-all duration-300 hover:scale-105">
    <Activity className="w-4 h-4 lg:w-5 lg:h-5 text-emerald-400 drop-shadow-lg" /> <span className="text-emerald-200 font-semibold text-sm lg:text-base tracking-wide drop-shadow-lg">{
    t('landing.realTime')
  }</span> </div> </div> {/* Action Buttons */
  } <div className="flex flex-col sm:flex-row gap-3 lg:gap-4 justify-center mb-8 lg:mb-12">
    <Button size="lg" className="macos-button bg-primary text-primary-foreground hover:bg-primary/90 px-6 lg:px-8 py-3 lg:py-4 text-base lg:text-lg font-semibold shadow-2xl hover:scale-105 transition-all duration-300"> {
    t('landing.getStarted')
  }
    <ArrowRight className="w-4 h-4 lg:w-5 lg:h-5 ml-2" />
    </Button>
    <Button variant="outline" size="lg" className="macos-button px-6 lg:px-8 py-3 lg:py-4 text-base lg:text-lg font-semibold border-2 border-border text-foreground hover:bg-accent transition-all duration-300 hover:scale-105"> {
    t('landing.watchDemo')
  }
    <Sparkles className="w-4 h-4 lg:w-5 lg:h-5 ml-2" />
    </Button> </div> {/* Performance Metrics */
  } <div className="grid grid-cols-3 gap-4 lg:gap-8 max-w-2xl mx-auto"> <div className="text-center group"> <div className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-cyan-400 via-blue-400 to-cyan-500 bg-clip-text text-transparent mb-1 lg:mb-2 drop-shadow-2xl group-hover:scale-110 transition-transform duration-300">98.5%</div> <div className="text-xs lg:text-sm text-muted-foreground font-medium tracking-wide drop-shadow-lg">{
    t('landing.accuracy')
  }</div> </div> <div className="text-center group"> <div className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 bg-clip-text text-transparent mb-1 lg:mb-2 drop-shadow-2xl group-hover:scale-110 transition-transform duration-300">&lt;2s</div> <div className="text-xs lg:text-sm text-muted-foreground font-medium tracking-wide drop-shadow-lg">{
    t('landing.responseTime')
  }</div> </div> <div className="text-center group"> <div className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-emerald-400 via-green-400 to-emerald-500 bg-clip-text text-transparent mb-1 lg:mb-2 drop-shadow-2xl group-hover:scale-110 transition-transform duration-300">99.9%</div> <div className="text-xs lg:text-sm text-muted-foreground font-medium tracking-wide drop-shadow-lg">{
    t('landing.uptime')
  }</div> </div> </div> </div> </div> </section> );
  };
  export default HeroSection;
