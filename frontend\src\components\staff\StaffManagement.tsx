import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/Button';
import DataTable from '../ui/DataTable';
import CRUDModal from '../ui/CRUDModal';
import { staffAPI } from '../../utils/api';
import { Loader2, Edit, Trash2, Plus } from 'lucide-react';

interface StaffMember {
  id: number;
  employee_id: string;
  user: {
    id: number;
    username: string;
    full_name: string;
    email: string;
    role: string;
  };
  department: {
    id: number;
    name: string;
    description: string;
  };
  position: string;
  employment_status: string;
  hire_date: string;
  salary?: number;
}

interface ShiftSchedule {
  id: number;
  staff_name: string;
  date: string;
  shift: string;
  status: string;
  hours_worked?: number;
}

const StaffManagement: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const [activeTab, setActiveTab] = useState<'staff' | 'schedules' | 'attendance' | 'performance'>('staff');
  const [staffMembers, setStaffMembers] = useState
    <StaffMember[]>([]);
  const [schedules, setSchedules] = useState
    <ShiftSchedule[]>([]);
  const [loading, setLoading] =
  useState(true);
  const [showModal, setShowModal] =
  useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [selectedStaff, setSelectedStaff] = useState
    <StaffMember | null>(null);
  const [actionLoading, setActionLoading] = useState<number | null>(null); // Load staff from API
  useEffect(() => {
    loadStaff();
  }, []);
  const loadStaff = async () => {
    setLoading(true);
    try {
      const response = await staffAPI.getStaffProfiles();
      setStaffMembers(response.results || []);
    } catch (error) {
      console.error('Failed to load staff:', error); // Fallback to mock data if API fails

const mockStaff: StaffMember[] = [ {
    id: 1, employee_id: 'EMP000001', user: {
    full_name: 'Dr. Sarah Johnson', email: '<EMAIL>', role: 'doctor',
  }, department: 'Cardiology', position: 'Senior Cardiologist', employment_status: 'active', hire_date: '2020-03-15', salary: 180000,
  }, {
    id: 2, employee_id: 'EMP000002', user: {
    full_name: 'Nurse Mary Wilson', email: '<EMAIL>', role: 'nurse',
  }, department: 'Emergency', position: 'Charge Nurse', employment_status: 'active', hire_date: '2019-08-22', salary: 75000,
  }, {
    id: 3, employee_id: 'EMP000003', user: {
    full_name: 'John Smith', email: '<EMAIL>', role: 'receptionist',
  }, department: 'Administration', position: 'Front Desk Coordinator', employment_status: 'active', hire_date: '2021-01-10', salary: 45000,
  }, ];
  const mockSchedules: ShiftSchedule[] = [ {
    id: 1, staff_name: 'Dr. Sarah Johnson', date: '2024-01-15', shift: 'Morning (7:00 AM - 3:00 PM)', status: 'scheduled', hours_worked: 8,
  }, {
    id: 2, staff_name: 'Nurse Mary Wilson', date: '2024-01-15', shift: 'Night (11:00 PM - 7:00 AM)', status: 'in_progress', hours_worked: 6,
  }, {
    id: 3, staff_name: 'John Smith', date: '2024-01-15', shift: 'Day (9:00 AM - 5:00 PM)', status: 'completed', hours_worked: 8,
  }, ];
  setStaffMembers(mockStaff);
  setSchedules(mockSchedules);
  } finally {
    setLoading(false);
  }
  }; // CRUD Handlers

const handleCreateStaff = () => {
    setSelectedStaff(null);
  setModalMode('create');
  setShowModal(true);
  };
  const handleEditStaff = (staff: StaffMember) => {
    setSelectedStaff(staff);
  setModalMode('edit');
  setShowModal(true);
  };
  const handleDeleteStaff = async (staffId: number) => {
    if (!confirm('Are you sure you want to delete this staff member?')) return;
  setActionLoading(staffId);
  try {
    await crudService.deleteStaffProfile(staffId);
  await loadStaff();
  } catch (error) {
    console.error('Failed to delete staff:', error);
  alert('Failed to delete staff member. Please try again.');
  } finally {
    setActionLoading(null);
  }
  };
  const handleSubmitStaff = async (staffData: any) => {
    try {
    if (modalMode === 'create') {
    await crudService.createStaffProfile(staffData);
  } else
  if (selectedStaff) {
    await crudService.updateStaffProfile(selectedStaff.id, staffData);
  } await loadStaff();
  } catch (error) {
    console.error('Failed to save staff:', error);
  throw error;
  }
  }; // Additional handlers

const handleScheduleStaff = (staff: StaffMember) => {
    alert(`Schedule management for ${
    staff.user.full_name
  }\n\nThis will open the scheduling interface to:\n- View current schedule\n- Assign new shifts\n- Manage time off requests\n- Set recurring schedules`); // TODO: Implement schedule management // setActiveTab('schedules'); // setSelectedStaffForScheduling(staff);
  };
  const handleEditSchedule = (schedule: ShiftSchedule) => {
    alert(`Edit schedule for ${
    schedule.staff_name
  }\n\nShift: ${
    schedule.shift
  }\nDate: ${
    schedule.date
  }\n\nThis will open the schedule editor.`); // TODO: Implement schedule editing
  };
  const handleClockIn = (schedule: ShiftSchedule) => {
    const now = new Date().toLocaleTimeString();
  alert(`Clock In for ${
    schedule.staff_name
  }\n\nTime: ${
    now
  }\nShift: ${
    schedule.shift
  }\n\nAttendance recorded successfully!`); // TODO: Implement actual clock in // await crudService.clockIn(schedule.id); // await loadSchedules();
  };
  const handleClockOut = (schedule: ShiftSchedule) => {
    const now = new Date().toLocaleTimeString();
  alert(`Clock Out for ${
    schedule.staff_name
  }\n\nTime: ${
    now
  }\nShift: ${
    schedule.shift
  }\n\nShift completed successfully!`); // TODO: Implement actual clock out // await crudService.clockOut(schedule.id); // await loadSchedules();
  }; // Form fields for staff modal

const staffFormFields = [ {
    key: 'employee_id', label: 'Employee ID', type: 'text' as const, required: true
  }, {
    key: 'full_name', label: 'Full Name', type: 'text' as const, required: true
  }, {
    key: 'email', label: 'Email', type: 'email' as const, required: true
  }, {
    key: 'role', label: 'Role', type: 'select' as const, required: true, options: [ {
    value: 'doctor', label: 'Doctor'
  }, {
    value: 'nurse', label: 'Nurse'
  }, {
    value: 'receptionist', label: 'Receptionist'
  }, {
    value: 'admin', label: 'Admin'
  } ]
  }, {
    key: 'department', label: 'Department', type: 'text' as const, required: true
  }, {
    key: 'position', label: 'Position', type: 'text' as const, required: true
  }, {
    key: 'employment_status', label: 'Employment Status', type: 'select' as const, required: true, options: [ {
    value: 'active', label: 'Active'
  }, {
    value: 'inactive', label: 'Inactive'
  }, {
    value: 'on_leave', label: 'On Leave'
  }, {
    value: 'suspended', label: 'Suspended'
  } ]
  }, {
    key: 'hire_date', label: 'Hire Date', type: 'date' as const, required: true
  }, {
    key: 'salary', label: 'Salary', type: 'number' as

const
  } ];
  const staffColumns = [ {
    key: 'employee_id', title: 'Employee ID', sortable: true, render: (value: string) => ( <span className="font-medium text-sky-700 dark:text-sky-400">{
    value
  }</span> ),
  }, {
    key: 'user.full_name', title: 'Name', sortable: true, render: (value: string, record: StaffMember) => ( <div> <div className="font-medium text-foreground">{
    value
  }</div> <div className="text-sm text-gray-500">{
    record.user.email
  }</div> </div> ),
  }, {
    key: 'user.role', title: 'Role', render: (value: string, record: StaffMember) => (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium status-info capitalize">
        {record.user?.role || 'N/A'}
      </span>
    ),
  }, {
    key: 'department', title: 'Department', sortable: true, render: (value: any, record: StaffMember) => (
      <span className="text-foreground">
        {record.department?.name || 'N/A'}
      </span>
    ),
  }, {
    key: 'position', title: 'Position',
  }, {
    key: 'employment_status', title: 'Status', render: (value: string) => {
    const colors = {
    active: 'status-success', inactive: 'bg-muted text-foreground', on_leave: 'status-warning', suspended: 'status-error',
  };
  return ( <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
    colors[value as keyof typeof colors] || 'bg-muted text-foreground'
  }`
  }> {
    value.replace('_', ' ')
  } </span> );
  },
  }, {
    key: 'hire_date', title: 'Hire Date', sortable: true, render: (value: string) => new Date(value).toLocaleDateString(),
  }, ];
  const scheduleColumns = [ {
    key: 'staff_name', title: 'Staff Member', sortable: true,
  }, {
    key: 'date', title: 'Date', sortable: true, render: (value: string) => new Date(value).toLocaleDateString(),
  }, {
    key: 'shift', title: 'Shift',
  }, {
    key: 'status', title: 'Status', render: (value: string) => {
    const colors = {
    scheduled: 'status-info', in_progress: 'status-warning', completed: 'status-success', absent: 'status-error',
  };
  return ( <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
    colors[value as keyof typeof colors] || 'bg-muted text-foreground'
  }`
  }> {
    value.replace('_', ' ')
  } </span> );
  },
  }, {
    key: 'hours_worked', title: 'Hours', render: (value: number) => value ? `${
    value
  }h` : '-',
  }, ];
  const renderStaffActions = (record: StaffMember) => ( <div className="flex items-center space-x-2">
    <Button size="sm" variant="outline" onClick={() => handleEditStaff(record)
  } >
    <Edit className="w-4 h-4 mr-1" /> Edit
    </Button>
    <Button size="sm" variant="destructive" onClick={() => handleDeleteStaff(record.id)
  } disabled={
    actionLoading === record.id
  } > {
    actionLoading === record.id ? (
    <Loader2 className="w-4 h-4 mr-1 animate-spin" /> ) : (
    <Trash2 className="w-4 h-4 mr-1" /> )
  } Delete
    </Button>
    <Button size="sm" variant="outline" onClick={() => handleScheduleStaff(record)
  } > Schedule
    </Button> </div> );
  const renderScheduleActions = (record: ShiftSchedule) => ( <div className="flex items-center space-x-2">
    <Button size="sm" variant="outline" onClick={() => handleEditSchedule(record)
  } > Edit
    </Button> {
    record.status === 'scheduled' && (
    <Button size="sm" onClick={() => handleClockIn(record)
  } > Clock In
    </Button> )
  } {
    record.status === 'in_progress' && (
    <Button size="sm" variant="destructive" onClick={() => handleClockOut(record)
  } > Clock Out
    </Button> )
  } </div> );
  const renderTabContent = () => {
    switch (activeTab) {
    case 'staff':
  return (
    <DataTable data={
    staffMembers
  } columns={
    staffColumns
  } loading={
    loading
  } actions={{
    title: t('common.actions'), render: renderStaffActions,
  }
  } /> );
  case 'schedules':
  return (
    <DataTable data={
    schedules
  } columns={
    scheduleColumns
  } loading={
    loading
  } actions={{
    title: t('common.actions'), render: renderScheduleActions,
  }
  } /> );
  case 'attendance':
  return ( <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg p-8 rounded-lg text-center"> <h3 className="text-lg font-medium text-foreground mb-2">{
    t('staff.attendanceTracking')
  }</h3> <p className="text-muted-foreground">{
    t('staff.attendanceFeatures')
  }</p> </div> );
  case 'performance':
  return ( <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg p-8 rounded-lg text-center"> <h3 className="text-lg font-medium text-foreground mb-2">{
    t('staff.performanceReviews')
  }</h3> <p className="text-muted-foreground">{
    t('staff.performanceFeatures')
  }</p> </div> );
  default: return null;
  }
  };
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */
  } <div className="flex items-center justify-between"> <div> <h1 className="text-2xl font-bold text-foreground">{
    t('staff.title')
  }</h1> <p className="text-muted-foreground">{
    t('staff.subtitle')
  }</p> </div>
    <Button onClick={
    handleCreateStaff
  }>
    <Plus className="w-4 h-4 mr-2" /> {
    t('staff.addStaff')
  }
    </Button> </div> {/* Stats Cards */
  } <div className="grid grid-cols-1 md:grid-cols-4 gap-6"> <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg p-6 rounded-lg"> <div className="flex items-center"> <div className="p-2 bg-blue-100 rounded-lg"> <svg className="w-6 h-6 text-sky-700 dark:text-sky-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /> </svg> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">Total Staff</p> <p className="text-2xl font-bold text-foreground">{
    staffMembers.length
  }</p> </div> </div> </div> <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg p-6 rounded-lg"> <div className="flex items-center"> <div className="p-2 bg-green-100 rounded-lg"> <svg className="w-6 h-6 text-emerald-700 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">On Duty Today</p> <p className="text-2xl font-bold text-foreground">24</p> </div> </div> </div> <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg p-6 rounded-lg"> <div className="flex items-center"> <div className="p-2 bg-yellow-100 rounded-lg"> <svg className="w-6 h-6 text-amber-700 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">On Leave</p> <p className="text-2xl font-bold text-foreground">3</p> </div> </div> </div> <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg p-6 rounded-lg"> <div className="flex items-center"> <div className="p-2 bg-purple-100 rounded-lg"> <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" /> </svg> </div> <div className="ml-4"> <p className="text-sm font-medium text-muted-foreground">Avg Performance</p> <p className="text-2xl font-bold text-foreground">4.2/5</p> </div> </div> </div> </div> {/* Tabs */
  } <div className="bg-background/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20 shadow-lg rounded-lg"> <div className="border-b border-border"> <nav className="-mb-px flex space-x-8 px-6"> {[ {
    id: 'staff', label: t('staff.staffDirectory'), icon: '👥'
  }, {
    id: 'schedules', label: t('staff.schedules'), icon: '📅'
  }, {
    id: 'attendance', label: t('staff.attendance'), icon: '⏰'
  }, {
    id: 'performance', label: t('staff.performance'), icon: '📊'
  }, ].map((tab) => ( <button key={
    tab.id
  } onClick={() => setActiveTab(tab.id as any)
  } className={`py-4 px-1 border-b-2 font-medium text-sm ${
    activeTab === tab.id ? 'border-blue-500 text-sky-700 dark:text-sky-400' : 'border-transparent text-gray-500 hover:text-foreground hover:border-border'
  }`
  } > <span className="mr-2">{
    tab.icon
  }</span> {
    tab.label
  } </button> ))
  } </nav> </div> <div className="p-6"> {
    renderTabContent()
  } </div> </div> {/* CRUD Modal */
  }
    <CRUDModal isOpen={
    showModal
  } onClose={() => setShowModal(false)
  } onSubmit={
    handleSubmitStaff
  } title={
    modalMode === 'create' ? 'Add New Staff Member' : 'Edit Staff Member'
  } fields={
    staffFormFields
  } initialData={
    selectedStaff || {
  }
  } mode={
    modalMode
  } /> </div> </div> );
  };
  export default StaffManagement;
