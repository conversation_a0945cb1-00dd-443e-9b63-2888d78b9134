import React from 'react';
  import {
    cn
  } from '../../utils/styleConverter';
  export interface RadioGroupProps extends React.HTMLAttributes
    <HTMLDivElement> {
    value?: string;
  onValueChange?: (value: string) => void;
  name?: string;
  error?: boolean;
  }

const RadioGroup = React.forwardRef
    <HTMLDivElement, RadioGroupProps>( ({
    className, value, onValueChange, name, error, children, ...props
  }, ref) => {
    return ( <div ref={
    ref
  } className={
    cn('grid gap-2', className)
  } role="radiogroup" {...props
  } > {
    React.Children.map(children, (child) => {
    if (React.isValidElement(child) && child.type === RadioGroupItem) {
    return React.cloneElement(child, {
    name, checked: child.props.value === value, onChange: () => onValueChange?.(child.props.value), error,
  });
  } return child;
  })
  } </div> );
  } );
  RadioGroup.displayName = 'RadioGroup';
  export interface RadioGroupItemProps extends Omit
    <React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
    value: string;
  error?: boolean;
  }

const RadioGroupItem = React.forwardRef
    <HTMLInputElement, RadioGroupItemProps>( ({
    className, error, ...props
  }, ref) => {
    return ( <div className="relative inline-flex items-center"> <input type="radio" ref={
    ref
  } className={
    cn( 'peer h-4 w-4 shrink-0 rounded-full border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 sr-only', error && 'border-red-500 focus-visible:ring-red-500', className )
  } {...props
  } /> <div className={
    cn( 'h-4 w-4 shrink-0 rounded-full border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 peer-checked:bg-primary peer-checked:border-primary flex items-center justify-center', error && 'border-destructive focus-visible:ring-destructive', )
  } > <div className="h-2 w-2 rounded-full bg-primary-foreground opacity-0 peer-checked:opacity-100 transition-opacity" /> </div> </div> );
  } );
  RadioGroupItem.displayName = 'RadioGroupItem';
  export {
    RadioGroup, RadioGroupItem
  };
