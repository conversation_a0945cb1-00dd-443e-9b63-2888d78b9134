{"search": "Search", "noData": "No data available", "loading": "Loading...", "common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "submit": "Submit", "reset": "Reset", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "back": "Back", "next": "Next", "previous": "Previous", "actions": "Actions", "status": "Status", "date": "Date", "time": "Time", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "notes": "Notes", "description": "Description", "total": "Total", "active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled", "patientId": "Patient ID", "doctor": "Doctor", "symptoms": "Symptoms", "cases": "Cases", "priority": "Priority", "high": "High", "medium": "Medium", "low": "Low", "adjustSearchCriteria": "Try adjusting your search criteria", "searchPlaceholder": "Search...", "all": "All", "ordered": "Ordered", "inProgress": "In Progress", "backToHome": "Back to Home", "tryAgain": "Try Again", "systemOnline": "System Online", "today": "Today", "todaysOverview": "Today's Overview", "quickActions": "Quick Actions", "recentPatients": "Recent Patients", "upcomingThisWeek": "Upcoming This Week", "noUpcomingAppointments": "No upcoming appointments", "todaysAppointments": "Today's Appointments", "newAppointment": "New Appointment", "scheduleAppointment": "Schedule Appointment", "patientRecords": "Patient Records", "prescriptions": "Prescriptions", "labResults": "Lab Results", "viewPatientRecords": "View and update patient files", "managePrescriptions": "Manage patient prescriptions", "reviewLabReports": "Review latest lab reports", "bookPatientAppointment": "Book a new patient appointment", "commonTasksShortcuts": "Common tasks and shortcuts", "appointmentsScheduled": "appointments scheduled", "searchAppointments": "Search appointments...", "noAppointmentsToday": "No appointments scheduled for today", "followUp": "Follow-up", "consultation": "Consultation", "checkUp": "Check-up", "lastBackupCompleted": "Last backup completed successfully", "addEditRemoveUsers": "Add, edit, or remove user accounts", "viewSystemReports": "View system and performance reports", "configureSystemSettings": "Configure system settings", "createSystemBackup": "Create system backup", "backupFunctionality": "Backup functionality will be implemented in the next update.", "multipleFailedLogins": "Multiple failed login attempts detected", "systemBackupCompleted": "System backup completed successfully", "databaseConnectionTimeout": "Database connection timeout", "hoursAgo": "hours ago", "systemOverview": "System Overview", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this item? This action cannot be undone.", "scheduled": "Scheduled", "confirmed": "Confirmed", "urgent": "<PERSON><PERSON>", "normal": "Normal", "available": "Available", "unavailable": "Unavailable", "scheduledForToday": "Scheduled for today", "completedToday": "Completed Today", "appointmentsCompleted": "Appointments completed", "thisWeek": "This Week", "upcomingAppointments": "Upcoming appointments", "totalPatients": "Total Patients", "underYourCare": "Under your care", "bookNewPatientAppointment": "Book a new patient appointment", "viewUpdatePatientFiles": "View and update patient files", "managePatientPrescriptions": "Manage patient prescriptions", "reviewLatestLabReports": "Review latest lab reports", "patient": "Patient", "type": "Type", "reason": "Reason", "complete": "Complete", "viewAppointment": "View appointment", "editAppointment": "Edit appointment", "selectedAppointment": "Selected appointment", "failedToCompleteAppointment": "Failed to complete appointment", "retry": "Retry", "settings": "Settings", "findPatient": "Find Patient", "searchPatientRecords": "Search patient records", "scheduleNewAppointment": "Schedule a new appointment"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "role": "Role", "loginButton": "Sign In", "registerButton": "Create Account", "forgotPassword": "Forgot Password?", "rememberMe": "Remember me", "welcomeBack": "Welcome back!", "createAccount": "Create your account", "invalidCredentials": "Invalid email or password", "registrationSuccess": "Account created successfully", "loginSuccess": "Login successful", "logoutSuccess": "Logout successful", "passwordResetSent": "Password reset email sent", "emailVerified": "Email verified successfully", "loginToAccount": "Sign in to your account", "emailPlaceholder": "Enter your email address", "passwordPlaceholder": "Enter your password", "noAccount": "Don't have an account?", "signUp": "Sign up"}, "register": {"title": "Create Your HMS AI Account", "subtitle": "Join thousands of healthcare providers transforming patient care with AI", "organizationType": {"label": "Organization Type", "hospital": "Hospital", "clinic": "Clinic", "pharmacy": "Pharmacy", "laboratory": "Laboratory", "other": "Other"}, "organizationName": "Organization Name", "organizationNamePlaceholder": "Enter your organization name", "firstName": "First Name", "firstNamePlaceholder": "Enter your first name", "lastName": "Last Name", "lastNamePlaceholder": "Enter your last name", "email": "Email Address", "emailPlaceholder": "Enter your email address", "phone": "Phone Number", "phonePlaceholder": "Enter your phone number", "password": "Password", "passwordPlaceholder": "Create a strong password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your password", "agreeToTerms": "I agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "subscribeNewsletter": "Subscribe to our newsletter for updates and healthcare insights", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "signIn": "Sign In"}, "navigation": {"dashboard": "Dashboard", "patients": "Patients", "appointments": "Appointments", "emergency": "Emergency", "staff": "Staff Management", "inventory": "Inventory", "billing": "Billing", "reports": "Reports", "settings": "Settings", "profile": "Profile", "help": "Help"}, "sidebar": {"expand": "Expand sidebar", "collapse": "Collapse sidebar"}, "dashboard": {"title": "Hospital Management System", "welcome": "Welcome", "overview": "Overview", "analytics": "Analytics", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "statistics": "Statistics", "totalPatients": "Total Patients", "monthlyRevenue": "Monthly Revenue", "appointmentsToday": "Appointments Today", "satisfactionRate": "Satisfaction Rate", "onDutyToday": "On Duty Today", "onLeave": "On Leave", "avgPerformance": "Avg Performance", "totalItems": "Total Items", "totalValue": "Total Value", "lowStockAlerts": "Low Stock Alerts", "expiringSoon": "Expiring Soon", "appointmentsTrend": "Appointments Trend", "revenueVsExpenses": "Revenue vs Expenses", "patientDemographics": "Patient Demographics", "departmentPerformance": "Department Performance", "ageGroups": "Age Groups", "patients": "Patients", "revenue": "Revenue", "chartPatients": "Patients", "chartRevenue": "Revenue ($)", "newAppointmentScheduled": "New appointment scheduled with <PERSON><PERSON>", "patientRegistered": "Patient <PERSON> registered", "paymentReceived": "Payment received from <PERSON> - $150", "labResultsReady": "Lab results ready for Patient ID P000123", "prescriptionIssued": "Prescription issued by <PERSON><PERSON>", "minutesAgo": "minutes ago", "hourAgo": "hour ago", "hoursAgo": "hours ago", "findPatient": "Find Patient", "searchPatientRecords": "Search patient records", "scheduleNewAppointment": "Schedule a new appointment", "doctorDashboard": "<PERSON>", "doctorDashboardDescription": "Manage your patients and medical practice", "todayAppointments": "Today's Appointments", "newAppointment": "New Appointment", "prescriptions": "Prescriptions", "labResults": "Lab Results", "managePrescriptions": "Manage patient prescriptions", "reviewLatestLabReports": "Review latest lab reports", "pendingReports": "Pending Reports", "completedToday": "Completed Today"}, "patients": {"title": "Patients", "subtitle": "Manage patient records and information", "addNew": "Add New Patient", "register": "Register Patient", "patientId": "Patient ID", "fullName": "Full Name", "dateOfBirth": "Date of Birth", "gender": "Gender", "bloodGroup": "Blood Group", "insuranceProvider": "Insurance Provider", "emergencyContact": "Emergency Contact", "medicalHistory": "Medical History", "allergies": "Allergies", "currentMedications": "Current Medications", "registrationSuccess": "Patient registered successfully", "male": "Male", "female": "Female", "other": "Other", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "medicalInfo": "Medical Information", "insuranceInfo": "Insurance Information", "firstName": "First Name", "lastName": "Last Name", "username": "Username", "phoneNumber": "Phone Number", "selectBloodGroup": "Select Blood Group", "knownAllergies": "Known Allergies", "listAllergies": "List any known allergies...", "chronicConditions": "Chronic Conditions", "listConditions": "List any chronic medical conditions...", "listMedications": "List current medications...", "policyNumber": "Policy Number", "emergencyContactName": "Emergency Contact Name", "emergencyContactPhone": "Emergency Contact Phone", "relationship": "Relationship", "relationshipPlaceholder": "e.g., Spouse, Parent, Sibling", "registering": "Registering...", "registerPatient": "Register Patient", "addNewPatient": "Add New Patient", "exportSelected": "Export Selected", "deleteSelected": "Delete Selected", "totalPatients": "Total Patients", "activeToday": "Active Today", "pendingAppointments": "Pending Appointments", "criticalCases": "Critical Cases", "patientList": "Patient List", "managePatientRecords": "Manage patient records and information", "errorLoadingPatients": "Error loading patients. Please try again.", "errorTitle": "Error"}, "appointments": {"title": "Appointment Calendar", "today": "Today", "monthNames": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "dayNames": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "appointmentDetails": "Appointment Details", "patientName": "Patient Name", "doctorName": "Doctor Name", "appointmentTime": "Appointment Time", "duration": "Duration", "type": "Type", "reason": "Reason for Visit", "status": "Status", "noAppointments": "No appointments scheduled for this day", "viewDetails": "View Details", "reschedule": "Reschedule", "cancel": "Cancel"}, "emergency": {"title": "Emergency Dashboard", "status": "Emergency Status", "currentlyManaging": "Currently managing", "activeCases": "active cases", "codeBlueAlert": "Code Blue Alert", "immediate": "Immediate", "urgent": "<PERSON><PERSON>", "lessUrgent": "<PERSON>", "nonUrgent": "Non-Urgent", "clinicCare": "Clinic Care", "quickActions": "Quick Actions", "newPatient": "New Patient", "triage": "Triage", "labOrders": "Lab Orders", "emergencyAlert": "Emergency Alert", "activeCasesTitle": "Active Emergency Cases", "caseId": "Case ID", "patient": "Patient", "chiefComplaint": "Chief <PERSON><PERSON><PERSON><PERSON>", "doctor": "Doctor", "arrived": "Arrived", "update": "Update", "bedStatus": "Bed Status", "occupied": "Occupied", "available": "Available"}, "staff": {"title": "Staff Management", "subtitle": "Manage hospital staff and employees", "addStaff": "Add Staff", "editStaff": "Edit Staff", "staffList": "Staff List", "employeeId": "Employee ID", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "department": "Department", "position": "Position", "hireDate": "Hire Date", "salary": "Salary", "status": "Status", "schedule": "Schedule", "qualifications": "Qualifications", "certifications": "Certifications", "emergencyContact": "Emergency Contact", "address": "Address", "dateOfBirth": "Date of Birth", "gender": "Gender", "nationality": "Nationality", "languages": "Languages", "specializations": "Specializations", "workShift": "Work Shift", "departments": {"emergency": "Emergency", "surgery": "Surgery", "pediatrics": "Pediatrics", "cardiology": "Cardiology", "neurology": "Neurology", "orthopedics": "Orthopedics", "radiology": "Radiology", "laboratory": "Laboratory", "pharmacy": "Pharmacy", "administration": "Administration"}, "positions": {"doctor": "Doctor", "nurse": "Nurse", "technician": "Technician", "administrator": "Administrator", "receptionist": "Receptionist", "pharmacist": "Pharmacist", "therapist": "Therapist"}, "shifts": {"morning": "Morning", "afternoon": "Afternoon", "night": "Night", "rotating": "Rotating"}, "staffDirectory": "Staff Directory", "schedules": "Schedules", "attendance": "Attendance", "performance": "Performance"}, "inventory": {"title": "Inventory Management", "subtitle": "Manage hospital inventory, supplies, and equipment", "addNewItem": "Add New Item", "importItems": "Import Items", "sku": "SKU", "itemName": "Item Name", "category": "Category", "type": "Type", "stock": "Stock", "minStock": "<PERSON>", "unitCost": "Unit Cost", "supplier": "Supplier", "expiry": "Expiry", "totalItems": "Total Items", "totalValue": "Total Value", "lowStockAlerts": "Low Stock Alerts", "expiringSoon": "Expiring Soon", "inventory": "Inventory", "alerts": "<PERSON><PERSON><PERSON>", "purchaseOrders": "Purchase Orders", "reports": "Reports", "edit": "Edit", "reorder": "Reorder", "history": "History", "resolve": "Resolve", "orderNow": "Order Now", "purchaseOrdersFeatures": "Purchase order management features coming soon...", "inventoryReports": "Inventory Reports", "inventoryReportsFeatures": "Inventory reporting features coming soon...", "alertType": "Alert <PERSON>", "currentStock": "Current Stock", "daysUntilExpiry": "Days Until Expiry", "lowStock": "Low Stock", "expired": "Expired"}, "forms": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "passwordTooShort": "Password must be at least 8 characters", "passwordsDoNotMatch": "Passwords do not match", "invalidPhone": "Please enter a valid phone number", "selectOption": "Please select an option", "enterValue": "Please enter a value", "invalidDate": "Please enter a valid date", "futureDate": "Date cannot be in the future", "pastDate": "Date cannot be in the past"}, "messages": {"success": "Operation completed successfully", "error": "An error occurred. Please try again.", "warning": "Please check your input and try again", "info": "Information updated", "confirmDelete": "Are you sure you want to delete this item?", "noData": "No data available", "loadingData": "Loading data...", "saveSuccess": "Data saved successfully", "deleteSuccess": "Item deleted successfully", "updateSuccess": "Item updated successfully", "createSuccess": "Item created successfully"}, "ui": {"featureComingSoon": "Feature coming soon...", "actions": "Actions", "refresh": "Refresh", "searchPlaceholder": "Search...", "itemsSelected": "item(s) selected", "showingResults": "Showing", "to": "to", "of": "of", "results": "results", "page": "Page", "pageOf": "of"}, "demo": {"arabicSupportDemo": "Arabic Language Support Demo", "completeArabicLocalization": "Complete Arabic localization with RTL (Right-to-Left) support", "currentLanguage": "Current Language", "textDirection": "Text Direction", "rtlRightToLeft": "RTL (Right-to-Left)", "ltrLeftToRight": "LTR (Left-to-Right)", "translations": "Translations", "rtlLayoutDemo": "RTL Layout Demo", "ltrLayoutEnglish": "LTR Layout (English)", "rtlLayoutArabic": "RTL Layout (Arabic)", "patientRegistration": "Patient Registration", "registerNewPatients": "Register new patients", "appointmentBooking": "Appointment Booking", "scheduleAppointments": "Schedule appointments", "arabicSupportFeatures": "Arabic Support Features", "completeTranslation": "Complete Translation", "allUIElementsTranslated": "All UI elements translated to Arabic", "rtlSupport": "RTL Support", "rightToLeftTextDirection": "Right-to-left text direction", "rtlLayout": "RTL Layout", "mirroredLayoutsForArabic": "Mirrored layouts for Arabic", "arabicTypography": "Arabic Typography", "optimizedFontsForArabic": "Optimized fonts for Arabic text", "responsiveRTL": "Responsive RTL", "mobileFriendlyRTL": "Mobile-friendly RTL design", "dynamicSwitching": "Dynamic Switching", "realTimeLanguageSwitching": "Real-time language switching", "howToTestArabicSupport": "How to Test Arabic Support", "clickLanguageSwitcher": "Click the language switcher in the top-right corner", "selectArabicFromDropdown": "Select \"العربية (Arabic)\" from the dropdown", "noticeRTLLayoutChange": "Notice the immediate RTL layout change", "navigateThroughPages": "Navigate through different pages to see translations", "testFormsTablesElements": "Test forms, tables, and interactive elements", "switchBackToEnglish": "Switch back to English to compare layouts"}, "audit": {"translationAuditReport": "Translation Audit Report", "completeAuditDescription": "Complete audit of all translation keys in the HMS application", "auditSummary": "<PERSON>t <PERSON>", "categories": "Categories", "translationKeys": "Translation Keys", "coverage": "Coverage", "rtlSupport": "RTL Support", "testingInstructions": "Testing Instructions", "switchLanguages": "Switch between English and Arabic using the language switcher", "navigatePages": "Navigate through all pages to verify translations", "testFormInputs": "Test form inputs, buttons, and interactive elements", "verifyRTLLayout": "Verify RTL layout changes when switching to Arabic", "checkTablePagination": "Check table pagination, search, and data display", "testErrorMessages": "Test error messages and validation feedback", "keys": "keys"}, "test": {"translationTestPage": "Translation Test Page", "testingAllTranslations": "Testing all translations in current language", "sampleUIComponents": "Sample UI Components with Translations", "buttons": "Buttons", "navigation": "Navigation", "formLabels": "Form Labels", "statusMessages": "Status Messages", "rtlLayoutTest": "RTL Layout Test", "textAlignment": "Text Alignment", "flexLayout": "Flex Layout", "howToUseTestPage": "How to Use This Test Page", "switchBetweenLanguages": "Switch between English and Arabic using the language switcher", "checkTranslationsCorrect": "Check that all translations appear correctly", "verifyRTLChanges": "Verify RTL layout changes when switching to Arabic", "testFormInteractions": "Test form inputs and button interactions", "ensureTextDirection": "Ensure text direction and alignment are correct", "translationCoverageReport": "Translation Coverage Report", "translationsCount": "translations"}, "admin": {"title": "Admin Dashboard", "welcome": "Welcome, Administrator", "systemOverview": "System Overview", "hospitalStats": "Hospital Statistics", "userManagement": "User Management", "systemHealth": "System Health", "totalUsers": "Total Users", "activeStaff": "Active Staff", "systemUptime": "System Uptime", "dataBackup": "Data Backup", "securityAlerts": "Security Alerts", "performanceMetrics": "Performance Metrics", "recentLogins": "Recent Logins", "systemLogs": "System Logs", "manageUsers": "Manage Users", "viewReports": "View Reports", "systemSettings": "System Settings", "backupData": "Backup Data", "quickActionsDescription": "Common administrative tasks and system management", "serverUptime": "Server Uptime", "avgResponseTime": "Avg Response Time", "activeSessions": "Active Sessions", "today": "Today"}, "doctor": {"title": "<PERSON>", "welcome": "Welcome, <PERSON>", "patientCare": "Patient Care", "todaySchedule": "Today's Schedule", "myPatients": "My Patients", "consultations": "Consultations", "patientsToday": "Patients Today", "pendingConsults": "Pending Consultations", "labResults": "Lab Results Pending", "prescriptions": "Prescriptions Written", "upcomingAppointments": "Upcoming Appointments", "recentConsultations": "Recent Consultations", "patientAlerts": "<PERSON><PERSON>", "medicalNotes": "Medical Notes", "viewPatient": "View Patient", "writeNotes": "Write Notes", "orderTests": "Order Tests", "prescribeMeds": "Prescribe Medication", "onDuty": "On Duty", "quickActionsDescription": "Essential medical tasks and patient management"}, "patient": {"title": "Patient Portal", "welcome": "Welcome", "healthOverview": "Health Overview", "myAppointments": "My Appointments", "medicalHistory": "Medical History", "healthSummary": "Health Summary", "nextAppointment": "Next Appointment", "activePrescriptions": "Active Prescriptions", "pendingTests": "Pending Tests", "recentVisits": "Recent Visits", "upcomingAppointments": "Upcoming Appointments", "testResults": "Test Results", "prescriptionHistory": "Prescription History", "healthRecords": "Health Records", "bookAppointment": "Book Appointment", "viewResults": "View Results", "downloadRecords": "Download Records", "contactDoctor": "Contact Doctor", "myMedicalRecords": "My Medical Records", "myPrescriptions": "My Prescriptions", "myLabResults": "My Lab Results", "appointmentBooking": "Book Appointment", "healthy": "Healthy", "activeMedications": "Active medications", "labTestsOrdered": "Lab tests ordered", "thisYear": "This year", "quickActionsDescription": "Manage your health and appointments", "next30Days": "Next 30 days", "availableDocuments": "Available documents", "currentMedications": "Current medications", "regularCheckup": "Regular check-up", "scheduleNewAppointment": "Schedule a new appointment", "viewMedicalHistory": "View your medical history", "viewCurrentMedications": "View current medications", "sendMessageToDoctor": "Send a message to your doctor", "patientPortal": "Patient Portal", "welcomeBack": "Welcome back", "emergency": "Emergency", "patientInformation": "Patient Information", "basicInformation": "Basic Information", "age": "Age", "years": "years", "bloodType": "Blood Type", "allergies": "Allergies", "emergencyContact": "Emergency Contact", "commonTasksServices": "Common tasks and services", "myHealthRecords": "My Health Records", "appointments": "Appointments", "medicalRecords": "Medical Records", "prescriptions": "Prescriptions", "noAppointmentsFound": "No appointments found", "viewAppointmentDetails": "View appointment details", "reschedule": "Reschedule", "rescheduleAppointment": "Reschedule appointment", "cancelAppointmentConfirm": "Are you sure you want to cancel this appointment?", "medicalRecordsDisplay": "Medical records will be displayed here", "prescriptionInfoDisplay": "Prescription information will be displayed here", "viewAllPrescriptions": "View All Prescriptions", "healthAlerts": "Health Alerts", "annualCheckupDue": "Annual Check-up Due", "annualPhysicalDue": "Your annual physical examination is due. Please schedule an appointment.", "prescriptionRefillReminder": "Prescription Refill Reminder", "bloodPressureMedRefill": "Your blood pressure medication refill is due in 3 days.", "generalPractice": "General Practice", "lastVisit": "Last Visit", "components": "Components", "languages": "Languages", "language": "Language", "data": "Data", "component": "Component", "dynamicSwitching": "Dynamic Language Switching"}, "medication": {"title": "Medication Administration", "description": "Manage patient medications and administration", "addMedication": "Add Medication", "dueNow": "Due Now", "overdue": "Overdue", "completedToday": "Completed Today", "totalMedications": "Total Medications", "dueMedications": "Due Medications", "administeredMedications": "Administered", "searchPlaceholder": "Search medications, patients, or rooms...", "filter": "Filter", "viewDetails": "View Details", "edit": "Edit", "administer": "Administer", "administered": "Administered", "administeredBy": "Administered by", "frequency": "Frequency", "notes": "Notes", "dosage": "Dosage", "route": "Route", "due": "Due", "room": "Room", "noMedicationsDue": "No medications due", "noMedicationsAdministered": "No medications administered today", "status": {"due": "due", "overdue": "overdue", "completed": "completed", "missed": "missed"}, "priority": {"high": "high", "normal": "normal", "low": "low"}}, "reports": {"title": "Reports & Analytics", "description": "Generate and view hospital reports and analytics", "generateReport": "Generate Report", "filter": "Filter", "patientReports": "Patient Reports", "patientReportsDesc": "Patient demographics, admissions, and discharge reports", "financialReports": "Financial Reports", "financialReportsDesc": "Revenue, billing, and financial analytics", "operationalReports": "Operational Reports", "operationalReportsDesc": "Staff performance, appointments, and operational metrics", "clinicalReports": "Clinical Reports", "clinicalReportsDesc": "Medical outcomes, treatment effectiveness, and clinical metrics", "recentReports": "Recent Reports", "patientDemographics": "Patient Demographics", "admissionReport": "Admission Report", "dischargeSummary": "Discharge Summary", "revenueReport": "Revenue Report", "billingSummary": "Billing Summary", "insuranceClaims": "Insurance Claims", "staffPerformance": "Staff Performance", "appointmentAnalytics": "Appointment Analytics", "departmentEfficiency": "Department Efficiency", "treatmentOutcomes": "Treatment Outcomes", "medicationUsage": "Medication Usage", "labTestAnalytics": "Lab Test Analytics", "lastGenerated": "Last Generated", "generatedBy": "Generated by", "downloads": "downloads", "view": "View", "download": "Download", "ready": "ready", "generating": "generating", "error": "error", "patientFlowAnalytics": "Patient Flow Analytics", "departmentPerformance": "Department Performance", "emergencyAdmissions": "Emergency Admissions", "scheduledAdmissions": "Scheduled Admissions", "averageStay": "Average Stay", "readmissionRate": "Readmission Rate", "thisMonth": "this month", "days": "days", "satisfaction": "satisfaction", "timePeriod": "Time Period", "week": "Week", "month": "Month", "quarter": "Quarter", "year": "Year"}, "nurse": {"title": "Nurse Dashboard", "welcome": "Welcome, Nurse", "patientsAssigned": "Patients Assigned", "medicationsDue": "Medications Due", "vitalsPending": "<PERSON><PERSON> Pending", "shiftHours": "Shift Hours", "patientAlerts": "<PERSON><PERSON>", "medicationSchedule": "Medication Schedule", "onDuty": "On Duty", "high": "High", "medium": "Medium", "low": "Low", "urgent": "<PERSON><PERSON>", "normal": "Normal", "quickActionsDescription": "Essential nursing tasks and patient care"}, "receptionist": {"title": "Receptionist Dashboard", "welcome": "Welcome, Receptionist", "appointmentsToday": "Appointments Today", "newRegistrations": "New Registrations", "pendingPayments": "Pending Payments", "phoneCalls": "Phone Calls", "upcomingAppointments": "Upcoming Appointments", "waitingPatients": "Waiting Patients", "registerPatient": "Register Patient", "addNewPatientToSystem": "Add new patient to system", "scheduleAppointment": "Schedule Appointment", "bookNewAppointment": "Book new appointment", "processPayment": "Process Payment", "handleBillingAndPayments": "Handle billing and payments", "patientRecords": "Patient Records", "accessPatientFiles": "Access patient files", "todayScheduleOverview": "Today's Schedule Overview", "totalAppointments": "Total Appointments", "confirmed": "Confirmed", "pending": "Pending", "onDuty": "On Duty", "quickActionsDescription": "Common front desk tasks and patient services"}, "patientRegistration": {"title": "Patient Registration", "subtitle": "Register a new patient in the system", "personalInfo": "Personal Information", "medicalInfo": "Medical Information", "emergencyContact": "Emergency Contact", "username": "Username", "email": "Email Address", "firstName": "First Name", "lastName": "Last Name", "phoneNumber": "Phone Number", "dateOfBirth": "Date of Birth", "address": "Address", "bloodGroup": "Blood Group", "allergies": "Allergies", "chronicConditions": "Chronic Conditions", "currentMedications": "Current Medications", "insuranceProvider": "Insurance Provider", "insurancePolicyNumber": "Insurance Policy Number", "emergencyContactName": "Emergency Contact Name", "emergencyContactPhone": "Emergency Contact Phone", "emergencyContactRelationship": "Relationship", "nextStep": "Next Step", "previousStep": "Previous Step", "register": "Register Patient", "step": "Step", "of": "of", "required": "Required field", "selectBloodGroup": "Select Blood Group", "enterAllergies": "Enter any known allergies", "enterConditions": "Enter any chronic conditions", "enterMedications": "Enter current medications", "relationship": {"spouse": "Spouse", "parent": "Parent", "child": "Child", "sibling": "Sibling", "friend": "Friend", "other": "Other"}}, "medical": {"title": "Medical Records", "subtitle": "Manage patient medical records and history", "patientRecords": "Patient Records", "addRecord": "Add Record", "editRecord": "Edit Record", "deleteRecord": "Delete Record", "recordDate": "Record Date", "diagnosis": "Diagnosis", "treatment": "Treatment", "notes": "Notes", "attachments": "Attachments", "vitals": "Vital Signs", "temperature": "Temperature", "bloodPressure": "Blood Pressure", "heartRate": "Heart Rate", "respiratoryRate": "Respiratory Rate", "oxygenSaturation": "Oxygen Saturation", "weight": "Weight", "height": "Height", "bmi": "BMI", "allergies": "Allergies", "medications": "Medications", "medicalHistory": "Medical History", "familyHistory": "Family History", "socialHistory": "Social History", "reviewOfSystems": "Review of Systems", "physicalExam": "Physical Examination", "labResults": "Lab Results", "imagingResults": "Imaging Results", "procedures": "Procedures", "followUp": "Follow-up", "referrals": "Referrals", "dischargeSummary": "Discharge Summary", "admissionNotes": "Admission Notes", "progressNotes": "Progress Notes", "operativeNotes": "Operative Notes", "consultationNotes": "Consultation Notes", "startByAddingRecord": "Start by adding a new medical record", "consultation": "Consultation", "admission": "Admission", "discharge": "Discharge", "procedure": "Procedure", "labResult": "Lab Result"}, "labTests": {"title": "Lab Test Management", "subtitle": "Manage laboratory tests and results", "orderTest": "Order Test", "testResults": "Test Results", "pendingTests": "Pending", "completedTests": "Completed Tests", "testName": "Test Name", "testCode": "Test Code", "orderDate": "Order Date", "collectionDate": "Collection Date", "resultDate": "Result Date", "normalRange": "Normal Range", "result": "Result", "status": "Status", "priority": "Priority", "specimen": "Specimen", "department": "Department", "technician": "Technician", "verifiedBy": "Verified By", "comments": "Comments", "criticalValues": "Critical Values", "abnormalResults": "Abnormal Results", "printReport": "Print Report", "emailReport": "Email Report", "testCategories": {"hematology": "Hematology", "chemistry": "Chemistry", "microbiology": "Microbiology", "immunology": "Immunology", "pathology": "Pathology", "radiology": "Radiology"}, "specimenTypes": {"blood": "Blood", "urine": "<PERSON><PERSON>", "stool": "Stool", "sputum": "Sputum", "csf": "CSF", "tissue": "Tissue"}, "sampleCollected": "<PERSON><PERSON> Collected", "totalTests": "Total Tests", "urgentTests": "<PERSON><PERSON>", "patient": "Patient", "testType": "Test Type", "ordered": "Ordered", "sample": "<PERSON><PERSON>", "results": "Results", "notes": "Notes", "noTestsFound": "No lab tests found", "startByOrdering": "Start by ordering a new lab test", "report": "Report"}, "prescriptions": {"title": "Prescription Management", "subtitle": "Manage patient prescriptions and medications", "newPrescription": "New Prescription", "activePrescriptions": "Active Prescriptions", "prescriptionHistory": "Prescription History", "medicationName": "Medication Name", "dosage": "Dosage", "frequency": "Frequency", "duration": "Duration", "instructions": "Instructions", "quantity": "Quantity", "refills": "Refills", "prescribedBy": "Prescribed By", "prescriptionDate": "Prescription Date", "startDate": "Start Date", "endDate": "End Date", "pharmacy": "Pharmacy", "drugInteractions": "Drug Interactions", "sideEffects": "Side Effects", "contraindications": "Contraindications", "renewPrescription": "Renew Prescription", "discontinue": "Discontinue", "modify": "Modify", "printPrescription": "Print Prescription", "electronicPrescription": "Electronic Prescription", "medicationCategories": {"antibiotics": "Antibiotics", "painkillers": "Pain Killers", "vitamins": "Vitamins", "supplements": "Supplements", "chronic": "Chronic Medications", "acute": "Acute Medications"}, "expiringSoon": "Expiring Soon", "expired": "Expired", "patient": "Patient", "start": "Start", "end": "End", "noPrescriptionsFound": "No prescriptions found", "startByCreating": "Start by creating a new prescription", "print": "Print"}, "landing": {"aiPowered": "AI-Powered Healthcare", "heroTitle": "Transform Healthcare with", "heroTitleHighlight": "Intelligent AI Solutions", "heroDescription": "Revolutionize patient care with our cutting-edge AI-powered hospital management system. Streamline operations, enhance diagnostics, and improve outcomes with intelligent automation.", "patientCare": "Patient Care", "secureData": "Secure Data", "realTime": "Real-time", "getStarted": "Get Started", "watchDemo": "Watch Demo", "hospitals": "Hospitals", "patients": "Patients", "uptime": "Uptime", "aiDiagnosis": "AI Diagnosis", "accuracy": "Accuracy", "responseTime": "Response Time", "seconds": "seconds", "features": {"badge": "Advanced Features", "title": "Comprehensive Healthcare Solutions", "subtitle": "Discover how our AI-powered platform transforms every aspect of hospital management with intelligent automation and data-driven insights.", "aiDiagnostics": {"title": "AI Diagnostics", "description": "Advanced machine learning algorithms assist doctors in accurate diagnosis and treatment recommendations."}, "patientCare": {"title": "Patient Care", "description": "Comprehensive patient management system with real-time monitoring and personalized care plans."}, "smartScheduling": {"title": "<PERSON> Scheduling", "description": "Intelligent appointment scheduling that optimizes doctor availability and reduces patient wait times."}, "digitalRecords": {"title": "Digital Records", "description": "Secure, cloud-based electronic health records accessible from anywhere with complete audit trails."}, "labManagement": {"title": "Lab Management", "description": "Streamlined laboratory operations with automated result processing and quality control."}, "pharmacyIntegration": {"title": "Pharmacy Integration", "description": "Seamless prescription management with automated drug interaction checks and inventory tracking."}, "analytics": {"title": "Analytics & Insights", "description": "Powerful business intelligence tools providing actionable insights for better decision making."}, "security": {"title": "Enterprise Security", "description": "Bank-level security with end-to-end encryption, HIPAA compliance, and regular security audits."}, "ctaTitle": "Ready to Transform Your Healthcare Operations?", "ctaDescription": "Join thousands of healthcare providers who trust our AI-powered platform to deliver exceptional patient care.", "startTrial": "Start Free Trial", "contactSales": "Contact Sales"}}, "header": {"nav": {"home": "Home", "product": "Product", "features": "Features", "integrations": "Integrations", "security": "Security", "api": "API", "solutions": "Solutions", "pricing": "Pricing", "resources": "Resources", "contact": "Contact"}, "signIn": "Sign In", "getStarted": "Get Started"}, "footer": {"newsletter": {"title": "Stay Updated with HMS AI", "description": "Get the latest updates on healthcare technology and AI innovations.", "placeholder": "Enter your email address", "subscribe": "Subscribe"}, "brand": {"description": "Empowering healthcare providers with intelligent AI solutions for better patient outcomes and operational efficiency.", "address": "123 Healthcare Ave, Medical District, NY 10001"}, "product": {"title": "Product", "features": "Features", "pricing": "Pricing", "demo": "Demo", "integrations": "Integrations"}, "company": {"title": "Company", "about": "About Us", "careers": "Careers", "news": "News", "contact": "Contact"}, "resources": {"title": "Resources", "documentation": "Documentation", "support": "Support", "community": "Community", "blog": "Blog"}, "legal": {"title": "Legal", "privacy": "Privacy Policy", "terms": "Terms of Service", "security": "Security", "compliance": "Compliance"}, "copyright": "All rights reserved."}, "testimonials": {"title": "Trusted by Healthcare Leaders Worldwide", "subtitle": "See how healthcare professionals are transforming patient care with our AI-powered platform.", "testimonial1": {"name": "Dr. <PERSON>", "role": "Chief Medical Officer", "hospital": "Metropolitan General Hospital", "content": "HMS AI has revolutionized our patient care workflow. The AI diagnostics feature has improved our accuracy by 40% and reduced diagnosis time significantly."}, "testimonial2": {"name": "Dr. <PERSON>", "role": "Hospital Director", "hospital": "Dubai Medical Center", "content": "The Arabic localization is exceptional. Our staff can now work efficiently in their native language while maintaining the highest standards of medical care."}, "testimonial3": {"name": "Dr. <PERSON>", "role": "Head of Emergency", "hospital": "City Emergency Hospital", "content": "The real-time patient monitoring and emergency management features have been game-changers for our department. Response times improved by 60%."}, "stats": {"hospitals": "Hospitals", "hospitalsDesc": "Healthcare facilities worldwide", "patients": "Patients", "patientsDesc": "Lives improved daily", "uptime": "Uptime", "uptimeDesc": "Reliable service guarantee", "support": "Support", "supportDesc": "Round-the-clock assistance"}, "statsTitle": "Trusted by Healthcare Professionals Globally", "statsSubtitle": "Our platform serves millions of patients and thousands of healthcare providers worldwide.", "ctaTitle": "Ready to Join the Healthcare Revolution?", "ctaDescription": "Transform your healthcare operations with our AI-powered platform. Start your journey towards better patient outcomes today.", "startTrial": "Start Free Trial", "scheduleDemo": "Schedule Demo"}, "billing": {"title": "Billing Management", "subtitle": "Manage patient billing and payments", "newBill": "New Bill", "billHistory": "Bill <PERSON>", "pendingPayments": "Pending Payments", "paidBills": "Paid <PERSON>", "overdueBills": "Overdue Bills", "billNumber": "<PERSON>", "patientName": "Patient Name", "billDate": "<PERSON>", "dueDate": "Due Date", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "balanceAmount": "Balance Amount", "paymentStatus": "Payment Status", "paymentMethod": "Payment Method", "services": "Services", "medications": "Medications", "procedures": "Procedures", "roomCharges": "Room Charges", "consultationFee": "Consultation Fee", "labTests": "Lab Tests", "imaging": "Imaging", "discount": "Discount", "tax": "Tax", "insurance": "Insurance", "insuranceClaim": "Insurance Claim", "copayment": "Co-payment", "deductible": "Deductible", "processPayment": "Process Payment", "generateInvoice": "Generate Invoice", "sendReminder": "Send Reminder", "writeOff": "Write Off", "refund": "Refund", "paymentMethods": {"cash": "Cash", "card": "Credit/Debit Card", "check": "Check", "bankTransfer": "Bank Transfer", "insurance": "Insurance", "online": "Online Payment"}, "billStatus": {"draft": "Draft", "sent": "<PERSON><PERSON>", "paid": "Paid", "overdue": "Overdue", "cancelled": "Cancelled", "refunded": "Refunded"}}, "users": {"title": "User Management", "subtitle": "Manage system users and permissions", "addUser": "Add User", "editUser": "Edit User", "userList": "User List", "username": "Username", "email": "Email", "role": "Role", "permissions": "Permissions", "lastLogin": "Last Login", "accountStatus": "Account Status", "createdDate": "Created Date", "modifiedDate": "Modified Date", "resetPassword": "Reset Password", "activateAccount": "Activate Account", "deactivateAccount": "Deactivate Account", "deleteUser": "Delete User", "userRoles": {"admin": "Administrator", "doctor": "Doctor", "nurse": "Nurse", "receptionist": "Receptionist", "pharmacist": "Pharmacist", "technician": "Technician", "patient": "Patient"}, "accountStatuses": {"active": "Active", "inactive": "Inactive", "suspended": "Suspended", "pending": "Pending"}}, "patientPortal": {"myAppointments": "My Appointments", "myMedicalRecords": "My Medical Records", "myPrescriptions": "My Prescriptions", "myLabResults": "My Lab Results", "myBills": "My Bills", "bookAppointment": "Book Appointment", "upcomingAppointments": "Upcoming Appointments", "pastAppointments": "Past Appointments", "appointmentHistory": "Appointment History", "medicalHistory": "Medical History", "familyHistory": "Family History", "allergiesAndConditions": "Allergies & Conditions", "currentMedications": "Current Medications", "medicationHistory": "Medication History", "dosageInstructions": "Dosage Instructions", "refillRequests": "Refill Requests", "requestRefill": "Request Refill", "labResultsHistory": "Lab Results History", "downloadResults": "Download Results", "normalValues": "Normal Values", "abnormalValues": "Abnormal Values", "pendingResults": "Pending Results", "billingHistory": "Billing History", "paymentHistory": "Payment History", "makePayment": "Make Payment", "downloadInvoice": "Download Invoice", "insuranceInfo": "Insurance Information", "contactInfo": "Contact Information", "emergencyContacts": "Emergency Contacts", "updateProfile": "Update Profile", "changePassword": "Change Password", "notifications": "Notifications", "preferences": "Preferences"}, "doctorPortal": {"myPatients": "My Patients", "mySchedule": "My Schedule", "patientList": "Patient List", "todayPatients": "Today's Patients", "patientSearch": "Patient Search", "patientDetails": "Patient Details", "medicalNotes": "Medical Notes", "addNote": "Add Note", "editNote": "Edit Note", "prescribemedication": "Prescribe Medication", "orderLabTests": "Order Lab Tests", "scheduleFollowUp": "Schedule Follow-up", "referPatient": "Refer Patient", "dischargePatient": "Discharge <PERSON>", "patientVitals": "<PERSON><PERSON>", "treatmentPlan": "Treatment Plan", "consultationNotes": "Consultation Notes", "scheduleManagement": "Schedule Management", "availableSlots": "Available Slots", "blockedSlots": "Blocked Slots", "appointmentRequests": "Appointment Requests", "approveAppointment": "Approve Appointment", "rescheduleAppointment": "Reschedule Appointment"}, "nursePortal": {"patientCare": "Patient Care", "assignedPatients": "Assigned Patients", "patientRounds": "Patient Rounds", "medicationAdministration": "Medication Administration", "vitalSigns": "Vital Signs", "recordVitals": "Record Vitals", "patientAssessment": "Patient Assessment", "careNotes": "Care Notes", "shiftReport": "Shift Report", "handoffReport": "Handoff Report", "patientEducation": "Patient Education", "dischargePreparation": "Discharge Preparation", "woundCare": "Wound Care", "painAssessment": "Pain Assessment", "fallRiskAssessment": "Fall Risk Assessment", "infectionControl": "Infection Control", "patientSafety": "Patient Safety"}, "appointmentBooking": {"title": "Book Appointment", "subtitle": "Schedule your appointment with our healthcare professionals", "selectDepartment": "Select Department", "selectDoctor": "Select Doctor", "selectDateTime": "Select Date & Time", "appointmentDetails": "Appointment Details", "confirmation": "Confirmation", "availableDates": "Available Dates", "availableTimeSlots": "Available Time Slots", "appointmentType": "Appointment Type", "reasonForVisit": "Reason for Visit", "reasonPlaceholder": "Please describe your symptoms or reason for the appointment...", "appointmentBookedSuccessfully": "Appointment Booked Successfully!", "confirmationEmailSent": "Your appointment has been scheduled and a confirmation will be sent to your email.", "appointmentDetailsTitle": "Appointment Details", "department": "Department", "doctor": "Doctor", "date": "Date", "time": "Time", "type": "Type", "printDetails": "Print Details", "bookAnother": "Book Another", "previous": "Previous", "next": "Next", "bookAppointment": "Book Appointment", "experience": "experience", "departments": {"cardiology": "Cardiology", "neurology": "Neurology", "orthopedics": "Orthopedics", "ophthalmology": "Ophthalmology", "general": "General Medicine"}, "departmentDescriptions": {"cardiology": "Heart and cardiovascular conditions", "neurology": "Brain and nervous system disorders", "orthopedics": "Bone, joint, and muscle conditions", "ophthalmology": "Eye and vision care", "general": "General health and wellness"}, "appointmentTypes": {"consultation": "Consultation", "checkup": "Regular Check-up", "emergency": "Emergency", "followup": "Follow-up"}, "appointmentTypeDescriptions": {"consultation": "Initial consultation or follow-up", "checkup": "Routine health examination", "emergency": "<PERSON>rgent medical attention needed", "followup": "Follow-up visit after treatment"}, "durations": {"consultation": "30 min", "checkup": "20 min", "emergency": "45 min", "followup": "15 min"}}}