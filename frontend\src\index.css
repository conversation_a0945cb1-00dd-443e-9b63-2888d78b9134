@tailwind base;
@tailwind components;
@tailwind utilities;

/* =================================
   HMS UNIFIED STYLE SYSTEM
   ================================= */

/* CSS Variables for Theme System */
@layer base {
  :root {
    /* Light Mode Colors */
    --background: 255 255 255;
    --foreground: 28 28 30;
    --card: 255 255 255;
    --card-foreground: 28 28 30;
    --popover: 255 255 255;
    --popover-foreground: 28 28 30;
    --primary: 59 130 246;
    --primary-foreground: 255 255 255;
    --secondary: 242 243 245;
    --secondary-foreground: 28 28 30;
    --muted: 242 243 245;
    --muted-foreground: 99 99 102;
    --accent: 248 249 250;
    --accent-foreground: 28 28 30;
    --destructive: 255 59 48;
    --destructive-foreground: 255 255 255;
    --border: 229 229 231;
    --input: 242 243 245;
    --ring: 59 130 246;
    --radius: 0.75rem;

    /* Glass Effects */
    --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-elevated: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  .dark {
    /* Dark Mode Colors */
    --background: 0 0 0;
    --foreground: 255 255 255;
    --card: 28 28 30;
    --card-foreground: 255 255 255;
    --popover: 28 28 30;
    --popover-foreground: 255 255 255;
    --primary: 59 130 246;
    --primary-foreground: 255 255 255;
    --secondary: 44 44 46;
    --secondary-foreground: 255 255 255;
    --muted: 44 44 46;
    --muted-foreground: 152 152 157;
    --accent: 44 44 46;
    --accent-foreground: 255 255 255;
    --destructive: 255 59 48;
    --destructive-foreground: 255 255 255;
    --border: 56 56 58;
    --input: 44 44 46;
    --ring: 147 197 253;

    /* Glass Effects */
    --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);
    --shadow-elevated: 0 12px 40px rgba(0, 0, 0, 0.4);
  }
}

/* Glassmorphism Effects - Consolidated */
@layer components {
  .glass {
    @apply backdrop-blur-md bg-card/80 border border-border/20 shadow-glass;
  }

  .glass-light {
    @apply backdrop-blur-sm bg-card/60 border border-border/10 shadow-sm;
  }

  .glass-heavy {
    @apply backdrop-blur-lg bg-card/90 border border-border/30 shadow-elevated;
  }
}

/* Clean Sidebar System */
@layer components {
  .sidebar-container {
    @apply relative z-10 transition-all duration-300 ease-in-out flex-shrink-0;
  }

  .sidebar-container.expanded {
    @apply w-64;
  }

  .sidebar-container.collapsed {
    @apply w-16;
  }

  .sidebar {
    @apply h-full w-full flex flex-col glass border-r border-border/50 shadow-2xl;
  }

  .sidebar-header {
    @apply p-4 border-b border-border/30;
  }

  .sidebar-nav {
    @apply flex-1 overflow-y-auto p-2 space-y-1;
  }

  .sidebar-nav-item {
    @apply w-full flex items-center px-3 py-2.5 rounded-lg transition-all duration-300 text-foreground/80 hover:text-foreground hover:bg-primary/10 relative;
  }

  .sidebar-nav-item.active {
    @apply bg-gradient-to-r from-primary/20 to-primary/10 text-primary border border-primary/20 shadow-sm;
  }

  .sidebar-nav-icon {
    @apply flex-shrink-0 transition-all duration-300 text-lg;
  }

  .sidebar-nav-text {
    @apply ml-3 font-medium transition-all duration-300 truncate;
  }

  /* Collapsed state styles */
  .sidebar-container.collapsed .sidebar-nav-text {
    @apply hidden;
  }

  .sidebar-container.collapsed .sidebar-nav-item {
    @apply justify-center px-2;
  }

  .sidebar-container.collapsed .sidebar-header h1 {
    @apply hidden;
  }

  .sidebar-container.collapsed .user-info {
    @apply hidden;
  }

  .sidebar-toggle {
    @apply absolute -right-3 top-6 z-20 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-200 cursor-pointer;
  }

  .sidebar-toggle:hover {
    @apply scale-110 bg-primary text-primary-foreground;
  }

  /* Tooltip for collapsed sidebar */
  .sidebar-tooltip {
    @apply absolute left-full ml-2 px-2 py-1 bg-popover text-popover-foreground text-sm rounded-md shadow-lg opacity-0 pointer-events-none transition-opacity duration-200 whitespace-nowrap z-50;
  }

  .sidebar-container.collapsed .sidebar-nav-item:hover .sidebar-tooltip {
    @apply opacity-100;
  }
}

/* Button System - Using Theme Variables */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-xl font-medium text-sm transition-all duration-200 border-0 cursor-pointer no-underline;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground shadow-lg hover:-translate-y-0.5 hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground shadow-lg hover:-translate-y-0.5 hover:shadow-xl;
  }

  .btn-success {
    @apply bg-primary text-primary-foreground shadow-lg hover:-translate-y-0.5 hover:shadow-xl hover:bg-primary/90;
  }

  .btn-danger {
    @apply bg-destructive text-destructive-foreground shadow-lg hover:-translate-y-0.5 hover:shadow-xl;
  }

  .btn-glass {
    @apply glass text-foreground px-4 py-2 rounded-xl font-medium transition-all duration-200 cursor-pointer hover:bg-card/90 hover:-translate-y-0.5;
  }
}

/* Card System - Using Theme Variables */
@layer components {
  .card {
    @apply glass rounded-2xl p-6 transition-all duration-300;
  }

  .card:hover {
    @apply -translate-y-1 shadow-elevated;
  }
}

/* Status System - Using Theme Variables */
@layer components {
  .status-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border;
  }

  .status-success {
    @apply bg-primary/10 text-primary border-primary/20 dark:bg-primary/20 dark:text-primary dark:border-primary/30;
  }

  .status-warning {
    @apply bg-muted text-muted-foreground border-border;
  }

  .status-error {
    @apply bg-destructive/10 text-destructive border-destructive/20;
  }

  .status-info {
    @apply bg-primary/10 text-primary border-primary/20;
  }

  .status-neutral {
    @apply bg-muted text-muted-foreground border-border;
  }
}

/* Unified Component Styles */
@layer components {
  .glass-card {
    @apply glass rounded-2xl p-6;
  }

  .glass-button {
    @apply btn-glass;
  }

  .glass-input {
    @apply backdrop-blur-md bg-input border border-border rounded-lg px-3 py-2 text-foreground transition-all duration-200 focus:outline-none focus:bg-card focus:border-ring focus:ring-2 focus:ring-ring/20;
  }

  .macos-text-tertiary {
    @apply text-muted-foreground/70;
  }

  .macos-accent-text {
    @apply text-primary;
  }
}

/* =================================
   ANIMATIONS
   ================================= */

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-slide-in {
  animation: slideInLeft 0.3s ease-out forwards;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out forwards;
}
