/**
 * MetricCard Component
 * Reusable card component for displaying metrics with icons, trends, and glassmorphism styling
 */
import React from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { cn } from '../../../lib/utils';
import { Card, CardContent } from '../../../components/ui/card';

interface MetricCardProps {
  label: string;
  value: string | number;
  change?: string;
  trend?: 'up' | 'down' | 'stable';
  icon?: React.ComponentType<{ className?: string }>;
  color?: 'blue' | 'green' | 'orange' | 'purple' | 'red';
  description?: string;
  className?: string;
  variant?: 'default' | 'glass' | 'solid';
  size?: 'sm' | 'md' | 'lg';
  showTrend?: boolean;
  showIcon?: boolean;
  onClick?: () => void;
  loading?: boolean;
}

const MetricCard: React.FC<MetricCardProps> = ({
  label,
  value,
  change,
  trend,
  icon: Icon,
  color,
  description,
  className,
  variant = 'glass',
  size = 'md',
  showTrend = true,
  showIcon = true,
  onClick,
  loading = false,
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-3 h-3" />;
  case 'down': return
    <TrendingDown className="w-3 h-3" />;
  default: return
    <Minus className="w-3 h-3" />;
  }
  };
  const getTrendColor = () => {
    switch (trend) {
    case 'up': return 'text-primary';
  case 'down': return 'text-destructive';
  default: return 'text-muted-foreground';
  }
  };
  const getSizeClasses = () => {
    switch (size) {
    case 'sm': return {
    card: 'p-4', icon: 'w-8 h-8', iconContainer: 'w-10 h-10', value: 'text-lg', label: 'text-xs', change: 'text-xs',
  };
  case 'lg': return {
    card: 'p-8', icon: 'w-8 h-8', iconContainer: 'w-16 h-16', value: 'text-3xl', label: 'text-base', change: 'text-sm',
  };
  default: return {
    card: 'p-6', icon: 'w-6 h-6', iconContainer: 'w-12 h-12', value: 'text-2xl', label: 'text-sm', change: 'text-sm',
  };
  }
  };
  const getVariantClasses = () => {
    switch (variant) {
    case 'solid': return 'bg-background dark:bg-gray-800 border border-border dark:border-gray-700 shadow-sm';
  case 'default': return 'bg-background border border-border shadow-sm';
  default: return 'glass border-0 shadow-lg';
  }
  };
  const sizeClasses = getSizeClasses();
  const variantClasses = getVariantClasses();
  if (loading) {
    return (
    <Card className={
    cn(variantClasses, className)
  }>
    <CardContent className={
    sizeClasses.card
  }> <div className="animate-pulse"> <div className="flex items-center justify-between mb-4"> <div> <div className="h-4 bg-accent dark:bg-gray-700 rounded w-24 mb-2"></div> <div className="h-8 bg-accent dark:bg-gray-700 rounded w-16"></div> </div> {
    showIcon && ( <div className={
    cn( 'rounded-xl bg-accent dark:bg-gray-700', sizeClasses.iconContainer )
  }></div> )
  } </div> {
    showTrend && change && ( <div className="h-3 bg-accent dark:bg-gray-700 rounded w-12"></div> )
  } </div>
    </CardContent>
    </Card> );
  }
  return (
    <Card className={
    cn( variantClasses, onClick && 'cursor-pointer hover:scale-105 transition-transform duration-200', className )
  } onClick={
    onClick
  } >
    <CardContent className={
    sizeClasses.card
  }> <div className="flex items-center justify-between"> <div className="flex-1"> <p className={
    cn( 'font-medium macos-text-secondary mb-2', sizeClasses.label )
  }> {
    label
  } </p> <p className={
    cn( 'font-bold macos-text-primary', sizeClasses.value )
  }> {
    value
  } </p> {
    description && ( <p className="text-xs macos-text-tertiary mt-1"> {
    description
  } </p> )
  } {
    showTrend && change && ( <div className="flex items-center mt-2"> <div className={
    cn('flex items-center gap-1', getTrendColor())
  }> {
    getTrendIcon()
  } <span className={
    cn('font-medium', sizeClasses.change)
  }> {
    change
  } </span> </div> </div> )
  } </div> {
    showIcon && ( <div className={
    cn( 'rounded-xl flex items-center justify-center shadow-lg text-white', sizeClasses.iconContainer, color )
  }>
    <Icon className={
    sizeClasses.icon
  } /> </div> )
  } </div>
    </CardContent>
    </Card> );
  };

export default MetricCard;

// Preset metric card variants
export const MetricCardSmall: React.FC<MetricCardProps> = (props) => (
  <MetricCard {...props} size="sm" />
);

const MetricCardLarge: React.FC<MetricCardProps> = (props) => (
  <MetricCard {...props} size="lg" />
);

export const MetricCardSolid: React.FC<MetricCardProps> = (props) => (
  <MetricCard {...props} variant="solid" />
);

// Metric card with custom content
interface CustomMetricCardProps {
  className?: string;
  variant?: 'default' | 'glass' | 'solid';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
}

export const CustomMetricCard: React.FC<CustomMetricCardProps> = ({
  className,
  variant = 'glass',
  size = 'md',
  children,
  onClick,
}) => {
  return (
    <div className={cn('p-6 bg-white/10 backdrop-blur-md border border-white/20 rounded-lg', className)}>
      {children}
    </div>
  );
};

// Metric grid component for displaying multiple metrics
interface MetricGridProps {
  metrics: (MetricCardProps & {
    id: string;
  })[];
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const MetricGrid: React.FC<MetricGridProps> = ({
  metrics,
  columns = 4,
  gap = 'md',
  className,
}) => {
  const getGridClasses = () => {
    const colClasses = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
    };

    const gapClasses = {
      sm: 'gap-3',
      md: 'gap-6',
      lg: 'gap-8',
    };

    return `grid ${colClasses[columns]} ${gapClasses[gap]}`;
  };

  return (
    <div className={cn(getGridClasses(), className)}>
      {metrics.map(({ id, ...metricProps }) => (
        <MetricCard key={id} {...metricProps} />
      ))}
    </div>
  );
};
