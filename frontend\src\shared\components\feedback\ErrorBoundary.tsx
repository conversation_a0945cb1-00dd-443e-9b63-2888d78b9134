/** * Enhanced Error Boundary Component * Provides comprehensive error handling with logging, recovery options, and user-friendly display */ import React, {
    Component, type ErrorInfo, type ReactNode
  } from 'react';
  import {
    AlertTriangle, RefreshCw, Home, Bug
  } from 'lucide-react';
  import {
    Button
  } from '../../../components/ui/Button';
  import {
    Card, CardContent, CardHeader, CardTitle
  } from '../../../components/ui/card';
  interface Props {
    children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  enableRecovery?: boolean;
  level?: 'page' | 'component' | 'global';
  } interface State {
    hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
  } export class ErrorBoundary extends Component
    <Props, State> {
    private retryCount = 0;
  private maxRetries = 3;
  constructor(props: Props) {
    super(props);
  this.state = {
    hasError: false, error: null, errorInfo: null, errorId: null,
  };
  } static getDerivedStateFromError(error: Error): Partial
    <State> { // Update state so the next render will show the fallback UI return {
    hasError: true, error, errorId: `error_${
    Date.now()
  }_${
    Math.random().toString(36).substr(2, 9)
  }`,
  };
  } componentDidCatch(error: Error, errorInfo: ErrorInfo) { // Log error details this.logError(error, errorInfo); // Update state with error info this.setState({
    error, errorInfo,
  }); // Call custom error handler if provided this.props.onError?.(error, errorInfo);
  } private logError = (error: Error, errorInfo: ErrorInfo) => {
    const errorDetails = {
    message: error.message, stack: error.stack, componentStack: errorInfo.componentStack, timestamp: new Date().toISOString(), userAgent: navigator.userAgent, url: window.location.href, userId: this.getCurrentUserId(), level: this.props.level || 'component',
  }; // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.group('🚨 Error Boundary Caught Error');
  console.error('Error:', error);
  console.error('Error Info:', errorInfo);
  console.error('Error Details:', errorDetails);
  console.groupEnd();
  } // Send to error reporting service in production
  if (process.env.NODE_ENV === 'production') {
    this.sendErrorReport(errorDetails);
  }
  };
  private getCurrentUserId = (): string | null => {
    try {
    const user = localStorage.getItem('user');
  return user ? JSON.parse(user).id : null;
  } catch {
    return null;
  }
  };
  private sendErrorReport = async (errorDetails: any) => {
    try { // Replace with your error reporting service await fetch('/api/errors', {
    method: 'POST', headers: { 'Content-Type': 'application/json',
  }, body: JSON.stringify(errorDetails),
  });
  } catch (reportingError) {
    console.error('Failed to send error report:', reportingError);
  }
  };
  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
    this.retryCount++;
  this.setState({
    hasError: false, error: null, errorInfo: null, errorId: null,
  });
  }
  };
  private handleReload = () => {
    window.location.reload();
  };
  private handleGoHome = () => {
    window.location.href = '/';
  };
  private handleReportBug = () => {
    const {
    error, errorInfo, errorId
  } = this.state;
  const subject = encodeURIComponent(`Bug Report - ${
    errorId
  }`);
  const body = encodeURIComponent(` Error ID: ${
    errorId
  } Error Message: ${
    error?.message
  } URL: ${
    window.location.href
  } Timestamp: ${
    new Date().toISOString()
  } Stack Trace: ${
    error?.stack
  } Component Stack: ${
    errorInfo?.componentStack
  } Please describe what you were doing when this error occurred: [Your description here] `);
  window.open(`mailto:<EMAIL>?subject=${
    subject
  }&body=${
    body
  }`);
  };
  render() {
    if (this.state.hasError) { // Custom fallback UI
  if (this.props.fallback) {
    return this.props.fallback;
  } // Default error UI
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-background dark:via-background dark:to-background flex items-center justify-center p-4">
    <Card className="w-full max-w-2xl glass">
    <CardHeader className="text-center"> <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
    <AlertTriangle className="w-8 h-8 text-destructive" /> </div>
    <CardTitle className="text-2xl font-bold text-foreground dark:text-white"> Oops! Something went wrong
    </CardTitle> <p className="text-muted-foreground dark:text-gray-300 mt-2"> We encountered an unexpected error. Don't worry, our team has been notified. </p>
    </CardHeader>
    <CardContent className="space-y-6"> {/* Error ID */
  } <div className="bg-muted dark:bg-gray-800/50 rounded-lg p-4"> <p className="text-sm text-muted-foreground dark:text-gray-400"> Error ID: <span className="font-mono font-medium">{
    this.state.errorId
  }</span> </p> </div> {/* Error Details (Development/Debug Mode) */
  } {(this.props.showDetails || process.env.NODE_ENV === 'development') && this.state.error && ( <details className="bg-destructive/10 border rounded-lg p-4"> <summary className="cursor-pointer font-medium text-destructive mb-2"> Technical Details </summary> <div className="space-y-2 text-sm"> <div> <strong>Error:</strong> <pre className="mt-1 p-2 bg-destructive/10 rounded text-xs overflow-auto"> {
    this.state.error.message
  } </pre> </div> {
    this.state.error.stack && ( <div> <strong>Stack Trace:</strong> <pre className="mt-1 p-2 bg-destructive/10 rounded text-xs overflow-auto max-h-32"> {
    this.state.error.stack
  } </pre> </div> )
  } </div> </details> )
  } {/* Action Buttons */
  } <div className="flex flex-col sm:flex-row gap-3"> {
    this.props.enableRecovery !== false && this.retryCount < this.maxRetries && (
    <Button onClick={
    this.handleRetry
  } className="flex items-center justify-center gap-2" variant="default" >
    <RefreshCw className="w-4 h-4" /> Try Again ({
    this.maxRetries - this.retryCount
  } attempts left)
    </Button> )
  }
    <Button onClick={
    this.handleReload
  } variant="outline" className="flex items-center justify-center gap-2" >
    <RefreshCw className="w-4 h-4" /> Reload Page
    </Button>
    <Button onClick={
    this.handleGoHome
  } variant="outline" className="flex items-center justify-center gap-2" >
    <Home className="w-4 h-4" /> Go Home
    </Button>
    <Button onClick={
    this.handleReportBug
  } variant="ghost" className="flex items-center justify-center gap-2" >
    <Bug className="w-4 h-4" /> Report Bug
    </Button> </div> {/* Help Text */
  } <div className="text-center text-sm text-gray-500 dark:text-gray-400"> <p> If this problem persists, please contact our support team at{' '
  } <a href="mailto:<EMAIL>" className="text-primary hover:underline" > <EMAIL> </a> </p> </div>
    </CardContent>
    </Card> </div> );
  } return this.props.children;
  }
  } // Higher-order component for wrapping components with error boundary export

function withErrorBoundary
    <P extends object>( Component: React.ComponentType
    <P>, errorBoundaryProps?: Omit
    <Props, 'children'> ) {
    const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps
  }>
    <Component {...props
  } />
    </ErrorBoundary> );
  WrappedComponent.displayName = `withErrorBoundary(${
    Component.displayName || Component.name
  })`;
  return WrappedComponent;
  } // Hook for error boundary context export

function
  useErrorHandler() {
    return (error: Error, errorInfo?: ErrorInfo) => { // This will trigger the nearest error boundary throw error;
  };
  } // Async error boundary for handling promise rejections export class AsyncErrorBoundary extends ErrorBoundary {
    componentDidMount() {
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection);
  } componentWillUnmount() {
    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);
  } private handleUnhandledRejection = (event: PromiseRejectionEvent) => {
    const error = new Error(event.reason?.message || 'Unhandled Promise Rejection');
  error.stack = event.reason?.stack;
  this.componentDidCatch(error, {
    componentStack: 'Async operation',
  });
  };
  } export default ErrorBoundary;
