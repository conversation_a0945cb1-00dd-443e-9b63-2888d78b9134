/** * Modal Component * Reusable modal component with glassmorphism styling and consistent behavior */ import React, {
    useEffect, useRef
  } from 'react';
  import {
    X
  } from 'lucide-react';
  import {
    Button
  } from '../../../components/ui/Button';
  import {
    cn
  } from '../../../lib/utils';
  import type {
    ModalConfig
  } from '../../types/common';
  interface ModalProps extends Omit
    <ModalConfig, 'content'> {
    isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  className?: string;
  overlayClassName?: string;
  variant?: 'default' | 'glass' | 'minimal';
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  position?: 'center' | 'top' | 'bottom';
  animation?: 'fade' | 'slide' | 'scale';
  closeOnEscape?: boolean;
  closeOnOverlayClick?: boolean;
  showCloseButton?: boolean;
  preventScroll?: boolean; 'data-testid'?: string;
  }

const Modal: React.FC
    <ModalProps> = ({
    isOpen, onClose, children, title, width, height, closable = true, maskClosable = true, footer, onOk, onCancel, className, overlayClassName, variant = 'glass', size = 'md', position = 'center', animation = 'scale', closeOnEscape = true, closeOnOverlayClick = true, showCloseButton = true, preventScroll = true, 'data-testid': testId,
  }) => {
    const modalRef = useRef
    <HTMLDivElement>(null);
  const previousActiveElement = useRef
    <HTMLElement | null>(null); // Handle escape key
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return;
  const handleEscape = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && closable) {
    onClose();
  }
  };
  document.addEventListener('keydown', handleEscape);
  return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, closable, onClose]); // Handle focus management
  useEffect(() => {
    if (isOpen) {
    previousActiveElement.current = document.activeElement as HTMLElement;
  modalRef.current?.focus();
  } else {
    previousActiveElement.current?.focus();
  }
  }, [isOpen]); // Handle body scroll
  useEffect(() => {
    if (!preventScroll) return;
  if (isOpen) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }
  return () => {
    document.body.style.overflow = '';
  };
  }, [isOpen, preventScroll]); // Handle overlay click

const handleOverlayClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget && closeOnOverlayClick && maskClosable && closable) {
    onClose();
  }
  }; // Handle close

const handleClose = () => {
    if (closable) {
    onCancel?.();
  onClose();
  }
  }; // Handle OK

const handleOk = () => {
    onOk?.();
  }; // Get size classes

const getSizeClasses = () => {
    if (width && height) {
    return {
    width, height
  };
  } switch (size) {
    case 'sm': return 'max-w-sm w-full mx-4';
  case 'lg': return 'max-w-4xl w-full mx-4';
  case 'xl': return 'max-w-6xl w-full mx-4';
  case 'full': return 'w-full h-full m-0';
  default: return 'max-w-2xl w-full mx-4';
  }
  }; // Get position classes

const getPositionClasses = () => {
    switch (position) {
    case 'top': return 'items-start pt-16';
  case 'bottom': return 'items-end pb-16';
  default: return 'items-center';
  }
  }; // Get animation classes

const getAnimationClasses = () => {
    const baseClasses = 'transition-all duration-300 ease-out';
  if (!isOpen) {
    switch (animation) {
    case 'slide': return `${
    baseClasses
  } translate-y-full opacity-0`;
  case 'fade': return `${
    baseClasses
  } opacity-0`;
  default: return `${
    baseClasses
  } scale-95 opacity-0`;
  }
  } return `${
    baseClasses
  } translate-y-0 scale-100 opacity-100`;
  }; // Get variant classes

const getVariantClasses = () => {
    switch (variant) {
    case 'minimal': return 'bg-background border border-border';
  case 'default': return 'bg-background border border-border shadow-xl';
  default: return 'glass border-0 shadow-2xl';
  }
  };
  if (!isOpen) return null;
  return ( <div className={
    cn( 'fixed inset-0 z-50 flex', getPositionClasses(), 'bg-background/50 backdrop-blur-sm', overlayClassName )
  } onClick={
    handleOverlayClick
  } data-testid={
    testId
  } > <div ref={
    modalRef
  } className={
    cn( 'relative rounded-xl overflow-hidden', getSizeClasses(), getVariantClasses(), getAnimationClasses(), className )
  } style={{
    width, height
  }
  } tabIndex={-1
  } role="dialog" aria-modal="true" aria-labelledby={
    title ? 'modal-title' : undefined
  } > {/* Header */
  } {(title || showCloseButton) && ( <div className="flex items-center justify-between p-6 border-b border-border"> {
    title && ( <h2 id="modal-title" className="text-xl font-semibold macos-text-primary"> {
    title
  } </h2> )
  } {
    showCloseButton && closable && (
    <Button variant="ghost" size="sm" onClick={
    handleClose
  } className="ml-auto" aria-label="Close modal" >
    <X className="w-4 h-4" />
    </Button> )
  } </div> )
  } {/* Content */
  } <div className="p-6 overflow-y-auto max-h-[calc(100vh-200px)]"> {
    children
  } </div> {/* Footer */
  } {(footer || onOk || onCancel) && ( <div className="flex items-center justify-end gap-3 p-6 border-t border-border"> {
    footer || ( <> {
    onCancel && (
    <Button variant="outline" onClick={
    handleClose
  }> Cancel
    </Button> )
  } {
    onOk && (
    <Button variant="glass-primary" onClick={
    handleOk
  }> OK
    </Button> )
  } </> )
  } </div> )
  } </div> </div> );
  };
  export default Modal;
// Preset modal variants export

const GlassModal: React.FC
    <ModalProps> = (props) => (
    <Modal {
...props
  } variant="glass" /> );
  export

const MinimalModal: React.FC
    <ModalProps> = (props) => (
    <Modal {
...props
  } variant="minimal" /> ); // Modal size variants export

const SmallModal: React.FC
    <ModalProps> = (props) => (
    <Modal {
...props
  } size="sm" /> );
  export

const LargeModal: React.FC
    <ModalProps> = (props) => (
    <Modal {
...props
  } size="lg" /> ); // Confirmation modal interface ConfirmModalProps {
    isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'info' | 'warning' | 'error' | 'success';
  } export

const ConfirmModal: React.FC
    <ConfirmModalProps> = ({
    isOpen, onClose, onConfirm, title, message, confirmText = 'Confirm', cancelText = 'Cancel', variant = 'info',
  }) => {
    const getVariantColor = () => {
    switch (variant) {
    case 'warning': return 'text-muted-foreground';
  case 'error': return 'text-destructive';
  case 'success': return 'text-primary';
  default: return 'text-primary';
  }
  };
  const getConfirmButtonVariant = () => {
    switch (variant) {
    case 'error': return 'destructive';
  case 'warning': return 'warning';
  default: return 'glass-primary';
  }
  };
  return (
    <Modal isOpen={
    isOpen
  } onClose={
    onClose
  } title={
    title
  } size="sm" variant="glass" footer={
    <div className="flex gap-3">
    <Button variant="outline" onClick={
    onClose
  }> {
    cancelText
  }
    </Button>
    <Button variant={
    getConfirmButtonVariant()
  } onClick={
    onConfirm
  }> {
    confirmText
  }
    </Button> </div>
  } > <p className={
    cn('text-base', getVariantColor())
  }>{
    message
  }</p>
    </Modal> );
  }; // Hook for modal state management export

const useModal = (initialState = false) => {
    const [isOpen, setIsOpen] = React.useState(initialState);
  const openModal = React.useCallback(() => setIsOpen(true), []);
  const closeModal = React.useCallback(() => setIsOpen(false), []);
  const toggleModal = React.useCallback(() => setIsOpen(prev => !prev), []);
  return {
    isOpen, openModal, closeModal, toggleModal,
  };
  };
