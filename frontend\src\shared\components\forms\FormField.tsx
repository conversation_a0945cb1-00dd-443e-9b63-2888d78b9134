/**
 * FormField Component
 * Reusable form field component with validation, icons, and consistent styling
 */
import React, { forwardRef } from 'react';
import { Eye, EyeOff, AlertCircle, Check } from 'lucide-react';
import { Input } from '../../../components/ui/Input';
import { Label } from '../../../components/ui/label';
import { Textarea } from '../../../components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select';
import { Checkbox } from '../../../components/ui/checkbox';
import type { FormField as FormFieldConfig } from '../../types/common';

interface FormFieldProps extends Omit<FormFieldConfig, 'name'> {
  name: string;
  value?: any;
  onChange?: (value: any) => void;
  onBlur?: () => void;
  error?: string;
  touched?: boolean;
  className?: string;
  variant?: 'glass' | 'solid' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  showValidation?: boolean;
  autoComplete?: string;
  'data-testid'?: string;
  icon?: React.ComponentType<{ className?: string }>;
  description?: string;
}

const FormField = forwardRef<HTMLInputElement, FormFieldProps>(({
  name,
  label,
  type = 'text',
  value = '',
  onChange,
  onBlur,
  required = false,
  placeholder,
  options = [],
  validation = [],
  disabled = false,
  hidden = false,
  icon: Icon,
  description,
  error,
  touched = false,
  className,
  variant = 'glass',
  size = 'md',
  showValidation = true,
  autoComplete,
  'data-testid': testId,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const [isValid, setIsValid] = React.useState<boolean | null>(null);

  // Validate field value
  const validateField = React.useCallback((fieldValue: any) => {
    if (!showValidation) return null;
    
    for (const rule of validation) {
      switch (rule.type) {
        case 'required':
          if (!fieldValue || (typeof fieldValue === 'string' && fieldValue.trim() === '')) {
            return rule.message;
          }
          break;
        case 'email':
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (fieldValue && !emailRegex.test(fieldValue)) {
            return rule.message;
          }
          break;
        case 'minLength':
          if (fieldValue && fieldValue.length < rule.value) {
            return rule.message;
          }
          break;
        case 'maxLength':
          if (fieldValue && fieldValue.length > rule.value) {
            return rule.message;
          }
          break;
        case 'pattern':
          if (fieldValue && !new RegExp(rule.value).test(fieldValue)) {
            return rule.message;
          }
          break;
      }
    }
    return null;
  }, [validation, showValidation]);

  // Handle value change
  const handleChange = (newValue: any) => {
    onChange?.(newValue);
    if (showValidation) {
      const validationError = validateField(newValue);
      setIsValid(validationError === null);
    }
  };

  // Handle blur
  const handleBlur = () => {
    onBlur?.();
    if (showValidation) {
      const validationError = validateField(value);
      setIsValid(validationError === null);
    }
  };

  // Get validation state
  const validationError = error || (touched ? validateField(value) : null);
  const hasError = Boolean(validationError);
  const hasSuccess = showValidation && touched && !hasError && value;

  // Base classes
  const baseClasses = `
    relative w-full transition-all duration-200
    ${size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base'}
    ${hidden ? 'hidden' : ''}
    ${className || ''}
  `;

  // Field classes based on variant
  const fieldClasses = `
    w-full px-3 py-2 rounded-lg border transition-all duration-200
    ${variant === 'glass' 
      ? 'bg-white/10 backdrop-blur-md border-white/20 text-white placeholder-white/60' 
      : variant === 'solid'
      ? 'bg-white border-gray-300 text-gray-900'
      : 'bg-transparent border-gray-300 text-gray-900'
    }
    ${hasError
      ? 'border-destructive focus:border-destructive focus:ring-destructive/20'
      : hasSuccess
      ? 'border-primary focus:border-primary focus:ring-primary/20'
      : 'focus:border-primary focus:ring-primary/20'
    }
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-opacity-80'}
    focus:outline-none focus:ring-2
    ${Icon ? 'pl-10' : ''}
    ${type === 'password' ? 'pr-10' : ''}
  `;

  // Render different field types
  const renderField = () => {
    switch (type) {
      case 'textarea':
        return (
          <Textarea
            ref={ref as any}
            name={name}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            className={fieldClasses}
            data-testid={testId}
            autoComplete={autoComplete}
            {...props}
          />
        );

      case 'select':
        return (
          <Select value={value} onValueChange={handleChange} disabled={disabled}>
            <SelectTrigger className={fieldClasses} data-testid={testId}>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={name}
              checked={value}
              onCheckedChange={handleChange}
              disabled={disabled}
              data-testid={testId}
            />
            <Label htmlFor={name} className="text-sm font-medium">
              {label}
            </Label>
          </div>
        );

      case 'radio':
        return (
          <div className="space-y-2">
            {options.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <input
                  type="radio"
                  id={`${name}-${option.value}`}
                  name={name}
                  value={option.value}
                  checked={value === option.value}
                  onChange={(e) => handleChange(e.target.value)}
                  onBlur={handleBlur}
                  disabled={disabled}
                  className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  data-testid={`${testId}-${option.value}`}
                />
                <Label htmlFor={`${name}-${option.value}`} className="text-sm font-medium">
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        );

      default:
        return (
          <Input
            ref={ref}
            type={type === 'password' && showPassword ? 'text' : type}
            name={name}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            className={fieldClasses}
            data-testid={testId}
            autoComplete={autoComplete}
            {...props}
          />
        );
    }
  };

  if (type === 'checkbox') {
    return (
      <div className={baseClasses}>
        {renderField()}
        {description && (
          <p className="mt-1 text-xs text-gray-500">{description}</p>
        )}
        {validationError && (
          <p className="mt-1 text-xs text-red-500 flex items-center">
            <AlertCircle className="w-3 h-3 mr-1" />
            {validationError}
          </p>
        )}
      </div>
    );
  }

  return (
    <div className={baseClasses}>
      {label && type !== 'checkbox' && (
        <Label htmlFor={name} className="block text-sm font-medium mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      
      <div className="relative">
        {Icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Icon className="h-4 w-4 text-gray-400" />
          </div>
        )}
        
        {renderField()}
        
        {type === 'password' && (
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setShowPassword(!showPassword)}
            tabIndex={-1}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
            ) : (
              <Eye className="h-4 w-4 text-gray-400 hover:text-gray-600" />
            )}
          </button>
        )}
        
        {showValidation && hasSuccess && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <Check className="h-4 w-4 text-green-500" />
          </div>
        )}
      </div>
      
      {description && (
        <p className="mt-1 text-xs text-gray-500">{description}</p>
      )}
      
      {validationError && (
        <p className="mt-1 text-xs text-red-500 flex items-center">
          <AlertCircle className="w-3 h-3 mr-1" />
          {validationError}
        </p>
      )}
    </div>
  );
});

FormField.displayName = 'FormField';

export default FormField;