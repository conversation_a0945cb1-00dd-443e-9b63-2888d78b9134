/**
 * Dashboard Layout Component
 * Provides consistent layout structure for all dashboard pages
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from '../../../utils/styleConverter';

interface DashboardLayoutProps {
  title?: string;
  subtitle?: string;
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
  iconColor?: string;
  actions?: React.ReactNode;
  badges?: Array<{
    label: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  }>;
  children: React.ReactNode;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | 'full';
  spacing?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'glass' | 'minimal';
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  title,
  subtitle,
  description,
  icon: Icon,
  iconColor = 'feature-blue',
  actions,
  badges = [],
  children,
  className,
  headerClassName,
  contentClassName,
  maxWidth = '7xl',
  spacing = 'md',
  variant = 'glass',
}) => {
  const { t } = useTranslation();

  const getMaxWidthClass = () => {
    const widthMap = {
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl',
      '2xl': 'max-w-2xl',
      '3xl': 'max-w-3xl',
      '4xl': 'max-w-4xl',
      '5xl': 'max-w-5xl',
      '6xl': 'max-w-6xl',
      '7xl': 'max-w-7xl',
      full: 'max-w-full',
    };
    return widthMap[maxWidth];
  };

  const getSpacingClass = () => {
    const spacingMap = {
      sm: 'space-y-4',
      md: 'space-y-6',
      lg: 'space-y-8',
    };
    return spacingMap[spacing];
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'glass':
        return 'glass';
      case 'minimal':
        return 'bg-transparent';
      default:
        return 'bg-card border border-border';
    }
  };

  const getIconColorClass = () => {
    const colorMap = {
      'feature-blue': 'text-primary',
      'feature-green': 'text-primary',
      'feature-purple': 'text-primary',
      'feature-orange': 'text-primary',
      'feature-red': 'text-destructive',
    };
    return colorMap[iconColor as keyof typeof colorMap] || 'text-primary';
  };

  return (
    <div className={cn('min-h-screen p-4 md:p-6 lg:p-8', className)}>
      <div className={cn('mx-auto', getMaxWidthClass(), getSpacingClass())}>
        {/* Header Section */}
        {(title || subtitle || description || Icon || actions || badges.length > 0) && (
          <div className={cn('mb-8', headerClassName)}>
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                {Icon && (
                  <div className={cn('p-3 rounded-lg', getVariantClasses())}>
                    <Icon className={cn('w-8 h-8', getIconColorClass())} />
                  </div>
                )}
                <div>
                  {title && (
                    <h1 className="text-3xl font-bold text-white mb-2">
                      {title}
                    </h1>
                  )}
                  {subtitle && (
                    <p className="text-lg text-gray-300 mb-2">
                      {subtitle}
                    </p>
                  )}
                  {description && (
                    <p className="text-sm text-gray-400 max-w-2xl">
                      {description}
                    </p>
                  )}
                  {badges.length > 0 && (
                    <div className="flex items-center space-x-2 mt-3">
                      {badges.map((badge, index) => (
                        <span
                          key={index}
                          className={cn(
                            'px-2 py-1 text-xs rounded-full',
                            badge.variant === 'secondary'
                              ? 'bg-secondary text-secondary-foreground'
                              : badge.variant === 'destructive'
                              ? 'bg-destructive/10 text-destructive'
                              : badge.variant === 'outline'
                              ? 'border border-border text-foreground'
                              : 'bg-primary/10 text-primary'
                          )}
                        >
                          {badge.label}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {actions && (
                <div className="flex items-center space-x-3">
                  {actions}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Content Section */}
        <div className={cn(contentClassName)}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;

// Preset layout variants
export const GlassDashboardLayout: React.FC<Omit<DashboardLayoutProps, 'variant'>> = (props) => (
  <DashboardLayout {...props} variant="glass" />
);

export const MinimalDashboardLayout: React.FC<Omit<DashboardLayoutProps, 'variant'>> = (props) => (
  <DashboardLayout {...props} variant="minimal" />
);

export const DefaultDashboardLayout: React.FC<Omit<DashboardLayoutProps, 'variant'>> = (props) => (
  <DashboardLayout {...props} variant="default" />
);

// Dashboard section component for organizing content
interface DashboardSectionProps {
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'glass' | 'transparent';
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export const DashboardSection: React.FC<DashboardSectionProps> = ({
  title,
  description,
  actions,
  children,
  className,
  variant = 'transparent',
  collapsible = false,
  defaultExpanded = true,
}) => {
  const [expanded, setExpanded] = React.useState(defaultExpanded);

  const getVariantClasses = () => {
    switch (variant) {
      case 'glass':
        return 'glass rounded-lg p-6';
      case 'default':
        return 'bg-card border border-border rounded-lg p-6';
      default:
        return 'bg-transparent';
    }
  };

  if (!title && !description && variant === 'transparent') {
    return <div className={className}>{children}</div>;
  }

  return (
    <div className={cn(getVariantClasses(), className)}>
      {(title || description) && (
        <div className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              {title && <h3 className="text-lg font-semibold text-white">{title}</h3>}
              {description && (
                <p className="text-sm text-gray-400 mt-1">{description}</p>
              )}
            </div>
            <div className="flex items-center space-x-3">
              {actions}
              {collapsible && (
                <button
                  onClick={() => setExpanded(!expanded)}
                  className="text-sm px-2 py-1 rounded hover:bg-accent text-foreground"
                >
                  {expanded ? '−' : '+'}
                </button>
              )}
            </div>
          </div>
        </div>
      )}
      {(!collapsible || expanded) && (
        <div className={title || description ? 'pt-0' : ''}>
          {children}
        </div>
      )}
    </div>
  );
};

// Quick actions component for dashboard
interface QuickActionsProps {
  actions: Array<{
    id: string;
    title: string;
    description: string;
    icon: React.ComponentType<{
      className?: string;
    }>;
    onClick: () => void;
    disabled?: boolean;
    badge?: string | number;
  }>;
  columns?: 2 | 3 | 4;
  className?: string;
}

export const QuickActions: React.FC<QuickActionsProps> = ({
  actions,
  columns = 4,
  className,
}) => {
  const getGridClasses = () => {
    const colClasses = {
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    };
    return `grid ${colClasses[columns]} gap-4`;
  };

  return (
    <div className={cn(getGridClasses(), className)}>
      {actions.map((action) => (
        <div
          key={action.id}
          className={cn(
            'relative p-4 glass rounded-lg hover:bg-card/90 transition-all cursor-pointer',
            action.disabled && 'opacity-50 cursor-not-allowed'
          )}
          onClick={action.disabled ? undefined : action.onClick}
        >
          {action.badge && (
            <div className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground text-xs rounded-full w-6 h-6 flex items-center justify-center">
              {action.badge}
            </div>
          )}
          <action.icon className="w-8 h-8 text-primary mb-3" />
          <h4 className="font-medium mb-1 text-foreground">{action.title}</h4>
          <p className="text-sm text-muted-foreground">{action.description}</p>
        </div>
      ))}
    </div>
  );
};