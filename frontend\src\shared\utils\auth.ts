/**
 * Centralized Authentication Utilities
 * Provides consistent token management and authentication state handling
 */

import { AUTH_CONFIG } from './constants';
import type { User } from '../../types/auth';

export class AuthUtils {
  /**
   * Get the access token from localStorage
   */
  static getToken(): string | null {
    return localStorage.getItem(AUTH_CONFIG.TOKEN_KEY);
  }

  /**
   * Get the refresh token from localStorage
   */
  static getRefreshToken(): string | null {
    return localStorage.getItem(AUTH_CONFIG.REFRESH_TOKEN_KEY);
  }

  /**
   * Get the stored user data from localStorage
   */
  static getStoredUser(): User | null {
    try {
      const userData = localStorage.getItem(AUTH_CONFIG.USER_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing stored user data:', error);
      return null;
    }
  }

  /**
   * Store authentication tokens
   */
  static setTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem(AUTH_CONFIG.TOKEN_KEY, accessToken);
    localStorage.setItem(AUTH_CONFIG.REFRESH_TOKEN_KEY, refreshToken);
  }

  /**
   * Store user data
   */
  static setUser(user: User): void {
    localStorage.setItem(AUTH_CONFIG.USER_KEY, JSON.stringify(user));
  }

  /**
   * Clear all authentication data
   */
  static clearAuth(): void {
    localStorage.removeItem(AUTH_CONFIG.TOKEN_KEY);
    localStorage.removeItem(AUTH_CONFIG.REFRESH_TOKEN_KEY);
    localStorage.removeItem(AUTH_CONFIG.USER_KEY);
  }

  /**
   * Check if user is authenticated (has valid token)
   */
  static isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) return false;

    // Check if token is expired
    try {
      const payload = this.parseJWTPayload(token);
      const currentTime = Date.now() / 1000;
      
      // Add buffer time to prevent edge cases
      const bufferTime = AUTH_CONFIG.TOKEN_EXPIRY_BUFFER / 1000;
      return payload.exp > (currentTime + bufferTime);
    } catch (error) {
      console.error('Error parsing token:', error);
      return false;
    }
  }

  /**
   * Parse JWT token payload
   */
  static parseJWTPayload(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      throw new Error('Invalid JWT token');
    }
  }

  /**
   * Check if token needs refresh (expires within buffer time)
   */
  static needsRefresh(): boolean {
    const token = this.getToken();
    if (!token) return false;

    try {
      const payload = this.parseJWTPayload(token);
      const currentTime = Date.now() / 1000;
      const bufferTime = AUTH_CONFIG.TOKEN_EXPIRY_BUFFER / 1000;
      
      return payload.exp <= (currentTime + bufferTime);
    } catch (error) {
      return true; // If we can't parse the token, it needs refresh
    }
  }

  /**
   * Get token expiration time
   */
  static getTokenExpiration(): Date | null {
    const token = this.getToken();
    if (!token) return null;

    try {
      const payload = this.parseJWTPayload(token);
      return new Date(payload.exp * 1000);
    } catch (error) {
      return null;
    }
  }

  /**
   * Get time until token expires (in milliseconds)
   */
  static getTimeUntilExpiry(): number | null {
    const expiration = this.getTokenExpiration();
    if (!expiration) return null;

    return expiration.getTime() - Date.now();
  }

  /**
   * Check if refresh token is valid
   */
  static hasValidRefreshToken(): boolean {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) return false;

    try {
      const payload = this.parseJWTPayload(refreshToken);
      const currentTime = Date.now() / 1000;
      return payload.exp > currentTime;
    } catch (error) {
      return false;
    }
  }

  /**
   * Create authorization header
   */
  static getAuthHeader(): Record<string, string> {
    const token = this.getToken();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  /**
   * Handle authentication error (clear tokens and redirect)
   */
  static handleAuthError(): void {
    this.clearAuth();
    
    // Only redirect if not already on login page
    if (window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  }

  /**
   * Validate authentication state consistency
   */
  static validateAuthState(): {
    isValid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];
    const token = this.getToken();
    const refreshToken = this.getRefreshToken();
    const user = this.getStoredUser();

    // Check for missing components
    if (token && !refreshToken) {
      issues.push('Access token exists but refresh token is missing');
    }
    
    if (token && !user) {
      issues.push('Access token exists but user data is missing');
    }

    if (!token && (refreshToken || user)) {
      issues.push('Missing access token but other auth data exists');
    }

    // Check token validity
    if (token && !this.isAuthenticated()) {
      issues.push('Access token is expired or invalid');
    }

    if (refreshToken && !this.hasValidRefreshToken()) {
      issues.push('Refresh token is expired or invalid');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Clean up invalid authentication state
   */
  static cleanupAuthState(): void {
    const validation = this.validateAuthState();
    
    if (!validation.isValid) {
      console.warn('Invalid auth state detected:', validation.issues);
      this.clearAuth();
    }
  }

  /**
   * Initialize authentication state on app startup
   */
  static initializeAuth(): {
    isAuthenticated: boolean;
    user: User | null;
    needsRefresh: boolean;
  } {
    // Clean up any invalid state first
    this.cleanupAuthState();

    const isAuthenticated = this.isAuthenticated();
    const user = this.getStoredUser();
    const needsRefresh = this.needsRefresh();

    return {
      isAuthenticated,
      user,
      needsRefresh
    };
  }

  /**
   * Format authentication status for debugging
   */
  static getAuthStatus(): string {
    const token = this.getToken();
    const refreshToken = this.getRefreshToken();
    const user = this.getStoredUser();
    const isAuth = this.isAuthenticated();
    const needsRef = this.needsRefresh();
    const expiry = this.getTokenExpiration();

    return `Auth Status:
- Has Token: ${!!token}
- Has Refresh: ${!!refreshToken}
- Has User: ${!!user}
- Is Authenticated: ${isAuth}
- Needs Refresh: ${needsRef}
- Token Expires: ${expiry?.toISOString() || 'N/A'}
- User Role: ${user?.role || 'N/A'}`;
  }
}

export default AuthUtils;
