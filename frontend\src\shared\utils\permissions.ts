/**
 * Frontend Permission Utilities
 * Centralized permission checking that matches backend logic
 */

import type { User } from '../../types/auth';
import { USER_ROLES, ROLE_PERMISSIONS } from './constants';

export class PermissionUtils {
  /**
   * Check if user has admin access (HMS admin OR Django superuser)
   * Use this for general admin permission checks
   */
  static hasAdminAccess(user: User | null): boolean {
    if (!user) return false;
    return user.is_admin || user.is_staff; // is_staff indicates Django superuser in frontend
  }

  /**
   * Check if user has HMS business admin access (role='admin')
   * Use this for HMS-specific admin features
   */
  static hasHMSAdminAccess(user: User | null): boolean {
    if (!user) return false;
    return user.role === USER_ROLES.ADMIN;
  }

  /**
   * Check if user has technical admin access (Django superuser)
   * Use this for Django admin interface and technical operations
   */
  static hasTechnicalAdminAccess(user: User | null): boolean {
    if (!user) return false;
    return user.is_staff; // is_staff indicates Django superuser in frontend
  }

  /**
   * Check if user can create new users
   */
  static canCreateUsers(user: User | null): boolean {
    return this.hasAdminAccess(user);
  }

  /**
   * Check if user can manage system settings
   */
  static canManageSystemSettings(user: User | null): boolean {
    return this.hasHMSAdminAccess(user);
  }

  /**
   * Check if user can access Django admin
   */
  static canAccessDjangoAdmin(user: User | null): boolean {
    return this.hasTechnicalAdminAccess(user);
  }

  /**
   * Check if user can access patient data
   */
  static canAccessPatientData(user: User | null, patientUserId?: number): boolean {
    if (!user) return false;
    
    // Admins can access all patient data
    if (this.hasAdminAccess(user)) return true;
    
    // Doctors can access patient data
    if (user.role === USER_ROLES.DOCTOR) return true;
    
    // Nurses can access patient data
    if (user.role === USER_ROLES.NURSE) return true;
    
    // Receptionists can access patient data
    if (user.role === USER_ROLES.RECEPTIONIST) return true;
    
    // Patients can only access their own data
    if (user.role === USER_ROLES.PATIENT && patientUserId) {
      return user.id === patientUserId;
    }
    
    return false;
  }

  /**
   * Check if user can modify appointments
   */
  static canModifyAppointments(user: User | null): boolean {
    if (!user) return false;
    
    return this.hasAdminAccess(user) || 
           user.role === USER_ROLES.DOCTOR || 
           user.role === USER_ROLES.RECEPTIONIST ||
           user.role === USER_ROLES.PATIENT; // Patients can modify their own appointments
  }

  /**
   * Check if user can view reports
   */
  static canViewReports(user: User | null): boolean {
    if (!user) return false;
    return this.hasAdminAccess(user) || user.role === USER_ROLES.DOCTOR;
  }

  /**
   * Check if user can manage inventory
   */
  static canManageInventory(user: User | null): boolean {
    if (!user) return false;
    return this.hasAdminAccess(user);
  }

  /**
   * Check if user can manage staff
   */
  static canManageStaff(user: User | null): boolean {
    if (!user) return false;
    return this.hasAdminAccess(user);
  }

  /**
   * Check if user can access emergency features
   */
  static canAccessEmergency(user: User | null): boolean {
    if (!user) return false;
    return this.hasAdminAccess(user) || 
           user.role === USER_ROLES.DOCTOR || 
           user.role === USER_ROLES.NURSE;
  }

  /**
   * Check if user can manage billing
   */
  static canManageBilling(user: User | null): boolean {
    if (!user) return false;
    return this.hasAdminAccess(user) || user.role === USER_ROLES.RECEPTIONIST;
  }

  /**
   * Get user's role display name
   */
  static getRoleDisplayName(role: string): string {
    const roleMap: Record<string, string> = {
      [USER_ROLES.ADMIN]: 'Administrator',
      [USER_ROLES.DOCTOR]: 'Doctor',
      [USER_ROLES.NURSE]: 'Nurse',
      [USER_ROLES.RECEPTIONIST]: 'Receptionist',
      [USER_ROLES.PATIENT]: 'Patient',
    };
    return roleMap[role] || 'Unknown Role';
  }

  /**
   * Get available navigation items for user role
   */
  static getNavigationItems(user: User | null): string[] {
    if (!user) return [];

    const baseItems = ['dashboard', 'profile'];
    
    switch (user.role) {
      case USER_ROLES.ADMIN:
        return [
          ...baseItems,
          'user-management',
          'patients',
          'appointments',
          'medical-records',
          'emergency',
          'staff',
          'inventory',
          'billing',
          'reports',
          'ai-services',
          'settings'
        ];
      
      case USER_ROLES.DOCTOR:
        return [
          ...baseItems,
          'patients',
          'appointments',
          'medical-records',
          'emergency',
          'ai-services'
        ];
      
      case USER_ROLES.NURSE:
        return [
          ...baseItems,
          'patients',
          'appointments',
          'medical-records',
          'emergency'
        ];
      
      case USER_ROLES.RECEPTIONIST:
        return [
          ...baseItems,
          'patients',
          'appointments',
          'billing'
        ];
      
      case USER_ROLES.PATIENT:
        return [
          ...baseItems,
          'appointments',
          'medical-records',
          'billing'
        ];
      
      default:
        return baseItems;
    }
  }

  /**
   * Check if user has permission for specific navigation item
   */
  static canAccessNavigationItem(user: User | null, item: string): boolean {
    if (!user) return false;
    return this.getNavigationItems(user).includes(item);
  }
}

export default PermissionUtils;
