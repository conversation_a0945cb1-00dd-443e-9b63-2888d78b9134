/**
 * Style Converter Utilities
 * Provides consistent styling functions for the HMS application
 */

// Status color mappings - Updated to use unified theme colors
export const getStatusColorClass = (status: string): string => {
  const statusMap: Record<string, string> = {
    'active': 'text-primary',
    'inactive': 'text-muted-foreground',
    'pending': 'text-muted-foreground',
    'completed': 'text-primary',
    'cancelled': 'text-destructive',
    'scheduled': 'text-primary',
    'confirmed': 'text-primary',
    'rejected': 'text-destructive',
    'approved': 'text-primary',
    'draft': 'text-muted-foreground',
    'published': 'text-primary',
    'archived': 'text-muted-foreground',
    'urgent': 'text-destructive',
    'high': 'text-destructive',
    'medium': 'text-muted-foreground',
    'low': 'text-primary',
    'critical': 'text-destructive',
    'warning': 'text-muted-foreground',
    'info': 'text-primary',
    'success': 'text-primary',
    'error': 'text-destructive',
  };

  return statusMap[status.toLowerCase()] || 'text-muted-foreground';
};

// Accent background classes - Using unified theme variables
export const getAccentBackgroundClass = (variant: string = 'default'): string => {
  const variantMap: Record<string, string> = {
    'default': 'bg-background',
    'primary': 'bg-primary/10',
    'secondary': 'bg-secondary',
    'success': 'bg-primary/10',
    'warning': 'bg-muted',
    'error': 'bg-destructive/10',
    'info': 'bg-primary/10',
    'muted': 'bg-muted',
  };

  return variantMap[variant] || variantMap.default;
};

// Text classes for consistent typography
export const getTextClasses = () => {
  return {
    heading: {
      h1: 'text-4xl font-bold tracking-tight text-foreground',
      h2: 'text-3xl font-semibold tracking-tight text-foreground',
      h3: 'text-2xl font-semibold tracking-tight text-foreground',
      h4: 'text-xl font-semibold tracking-tight text-foreground',
      h5: 'text-lg font-semibold tracking-tight text-foreground',
      h6: 'text-base font-semibold tracking-tight text-foreground',
    },
    body: {
      large: 'text-lg text-foreground',
      default: 'text-base text-foreground',
      small: 'text-sm text-foreground',
      xs: 'text-xs text-foreground',
    },
    muted: {
      large: 'text-lg text-muted-foreground',
      default: 'text-base text-muted-foreground',
      small: 'text-sm text-muted-foreground',
      xs: 'text-xs text-muted-foreground',
    },
    accent: {
      primary: 'text-primary',
      secondary: 'text-secondary',
      success: 'text-primary',
      warning: 'text-muted-foreground',
      error: 'text-destructive',
      info: 'text-primary',
    },
  };
};

// Glass morphism effect classes using theme variables
export const getGlassClasses = (intensity: 'light' | 'medium' | 'heavy' = 'medium'): string => {
  const intensityMap: Record<string, string> = {
    light: 'backdrop-blur-sm bg-card/60 border border-border/10',
    medium: 'backdrop-blur-md bg-card/80 border border-border/20',
    heavy: 'backdrop-blur-lg bg-card/90 border border-border/30',
  };

  return intensityMap[intensity];
};

// Card variant classes
export const getCardClasses = (variant: 'default' | 'glass' | 'elevated' = 'default'): string => {
  const variantMap: Record<string, string> = {
    default: 'bg-card text-card-foreground border border-border shadow-sm',
    glass: `${getGlassClasses('medium')} text-foreground shadow-lg`,
    elevated: 'bg-card text-card-foreground border border-border shadow-lg hover:shadow-xl transition-shadow',
  };

  return variantMap[variant];
};

// Button variant classes using theme variables
export const getButtonClasses = (variant: 'default' | 'primary' | 'secondary' | 'ghost' | 'glass' = 'default'): string => {
  const variantMap: Record<string, string> = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    glass: `${getGlassClasses('light')} text-foreground hover:bg-card/90`,
  };

  return variantMap[variant];
};

// Utility function to combine classes
export const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

// Priority level styling using unified color system
export const getPriorityClasses = (priority: 'low' | 'medium' | 'high' | 'urgent' | 'critical'): string => {
  const priorityMap: Record<string, string> = {
    low: 'bg-primary/10 text-primary border border-primary/20',
    medium: 'bg-muted text-muted-foreground border border-border',
    high: 'bg-muted text-muted-foreground border border-border',
    urgent: 'bg-destructive/10 text-destructive border border-destructive/20',
    critical: 'bg-destructive/20 text-destructive border border-destructive/30',
  };

  return priorityMap[priority] || priorityMap.medium;
};

// Role-based styling using unified color system
export const getRoleClasses = (role: 'admin' | 'doctor' | 'nurse' | 'patient' | 'staff'): string => {
  const roleMap: Record<string, string> = {
    admin: 'bg-primary/10 text-primary border border-primary/20',
    doctor: 'bg-primary/10 text-primary border border-primary/20',
    nurse: 'bg-primary/10 text-primary border border-primary/20',
    patient: 'bg-muted text-muted-foreground border border-border',
    staff: 'bg-primary/10 text-primary border border-primary/20',
  };

  return roleMap[role] || roleMap.staff;
};

// Animation classes
export const getAnimationClasses = () => {
  return {
    fadeIn: 'animate-in fade-in duration-200',
    fadeOut: 'animate-out fade-out duration-200',
    slideIn: 'animate-in slide-in-from-bottom-4 duration-300',
    slideOut: 'animate-out slide-out-to-bottom-4 duration-300',
    scaleIn: 'animate-in zoom-in-95 duration-200',
    scaleOut: 'animate-out zoom-out-95 duration-200',
    pulse: 'animate-pulse',
    spin: 'animate-spin',
    bounce: 'animate-bounce',
  };
};

export default {
  getStatusColorClass,
  getAccentBackgroundClass,
  getTextClasses,
  getGlassClasses,
  getCardClasses,
  getButtonClasses,
  cn,
  getPriorityClasses,
  getRoleClasses,
  getAnimationClasses,
};
