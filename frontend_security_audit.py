"""
Frontend Security Audit for HMS
Comprehensive security analysis of frontend code, authentication, and access control
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime


class FrontendSecurityAudit:
    """
    Comprehensive frontend security audit
    """
    
    def __init__(self, frontend_path="frontend/src"):
        self.frontend_path = Path(frontend_path)
        self.issues = []
        self.warnings = []
        self.stats = {}
        
    def log_issue(self, category, file_path, message, severity='ERROR', line_number=None):
        """Log a security issue"""
        issue = {
            'category': category,
            'file': str(file_path),
            'message': message,
            'severity': severity,
            'line': line_number,
            'timestamp': datetime.now()
        }
        
        if severity == 'ERROR':
            self.issues.append(issue)
        else:
            self.warnings.append(issue)
    
    def scan_authentication_patterns(self):
        """Scan for authentication security patterns"""
        print("🔐 Scanning Authentication Patterns...")
        
        auth_files = [
            'store/slices/authSlice.ts',
            'shared/utils/auth.ts',
            'components/auth/ProtectedRoute.tsx',
            'utils/api.ts'
        ]
        
        for file_path in auth_files:
            full_path = self.frontend_path / file_path
            if full_path.exists():
                self.analyze_auth_file(full_path)
            else:
                self.log_issue('AUTH_FILES', file_path, "Authentication file not found")
    
    def analyze_auth_file(self, file_path):
        """Analyze individual authentication file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # Check for hardcoded secrets
            secret_patterns = [
                r'(api[_-]?key|secret|password|token)\s*[:=]\s*["\'][^"\']{10,}["\']',
                r'Bearer\s+[A-Za-z0-9\-_]{20,}',
                r'["\'][A-Za-z0-9+/]{40,}={0,2}["\']'  # Base64 patterns
            ]
            
            for i, line in enumerate(lines, 1):
                for pattern in secret_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        self.log_issue('HARDCODED_SECRETS', file_path, 
                                     f"Potential hardcoded secret found", 'ERROR', i)
            
            # Check for insecure token storage
            insecure_storage_patterns = [
                r'localStorage\.setItem\(["\'](?!token|refreshToken)[^"\']*token',
                r'sessionStorage\.setItem\(["\'][^"\']*token',
                r'document\.cookie\s*=.*token'
            ]
            
            for i, line in enumerate(lines, 1):
                for pattern in insecure_storage_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        self.log_issue('INSECURE_STORAGE', file_path,
                                     f"Potentially insecure token storage", 'WARNING', i)
            
            # Check for proper error handling
            if 'catch' in content and 'console.log' in content:
                for i, line in enumerate(lines, 1):
                    if 'console.log' in line and any(err in line.lower() for err in ['error', 'catch']):
                        self.log_issue('ERROR_EXPOSURE', file_path,
                                     f"Error information may be exposed in console", 'WARNING', i)
            
        except Exception as e:
            self.log_issue('FILE_READ_ERROR', file_path, f"Could not read file: {str(e)}")
    
    def scan_permission_patterns(self):
        """Scan for permission and access control patterns"""
        print("🛡️ Scanning Permission Patterns...")
        
        permission_files = [
            'shared/utils/permissions.ts',
            'shared/utils/constants.ts',
            'components/dashboard/RoleDashboard.tsx',
            'components/dashboard/Dashboard.tsx'
        ]
        
        for file_path in permission_files:
            full_path = self.frontend_path / file_path
            if full_path.exists():
                self.analyze_permission_file(full_path)
            else:
                self.log_issue('PERMISSION_FILES', file_path, "Permission file not found")
    
    def analyze_permission_file(self, file_path):
        """Analyze permission-related files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # Check for role-based access control implementation
            if 'permissions.ts' in str(file_path):
                required_functions = [
                    'hasAdminAccess',
                    'hasHMSAdminAccess',
                    'canAccessPatientData',
                    'getNavigationItems'
                ]
                
                for func in required_functions:
                    if func not in content:
                        self.log_issue('MISSING_PERMISSION_FUNCTION', file_path,
                                     f"Missing required permission function: {func}")
            
            # Check for hardcoded role checks
            hardcoded_role_patterns = [
                r'user\.role\s*===?\s*["\']admin["\']',
                r'user\.role\s*===?\s*["\']doctor["\']',
                r'user\.role\s*===?\s*["\']patient["\']'
            ]
            
            for i, line in enumerate(lines, 1):
                for pattern in hardcoded_role_patterns:
                    if re.search(pattern, line):
                        self.log_issue('HARDCODED_ROLES', file_path,
                                     f"Hardcoded role check found - should use permission utils", 'WARNING', i)
            
            # Check for proper null/undefined checks
            unsafe_access_patterns = [
                r'user\.[a-zA-Z_]+(?!\s*[?&|])',  # Direct property access without null check
                r'\.role(?!\s*[?&|])'  # Direct role access
            ]
            
            for i, line in enumerate(lines, 1):
                if 'user.' in line and '?' not in line and 'if' not in line:
                    for pattern in unsafe_access_patterns:
                        if re.search(pattern, line):
                            self.log_issue('UNSAFE_ACCESS', file_path,
                                         f"Potentially unsafe property access", 'WARNING', i)
            
        except Exception as e:
            self.log_issue('FILE_READ_ERROR', file_path, f"Could not read file: {str(e)}")
    
    def scan_api_security_patterns(self):
        """Scan for API security patterns"""
        print("🌐 Scanning API Security Patterns...")
        
        # Find all service files
        services_path = self.frontend_path / 'services'
        if services_path.exists():
            for service_file in services_path.glob('*.ts'):
                self.analyze_api_service_file(service_file)
        
        # Check utils/api.ts
        api_utils_path = self.frontend_path / 'utils' / 'api.ts'
        if api_utils_path.exists():
            self.analyze_api_service_file(api_utils_path)
    
    def analyze_api_service_file(self, file_path):
        """Analyze API service files for security issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # Check for hardcoded API URLs
            hardcoded_url_patterns = [
                r'https?://[^"\']+',
                r'http://localhost:\d+',
                r'http://127\.0\.0\.1:\d+'
            ]
            
            for i, line in enumerate(lines, 1):
                for pattern in hardcoded_url_patterns:
                    if re.search(pattern, line) and 'import.meta.env' not in line:
                        self.log_issue('HARDCODED_URLS', file_path,
                                     f"Hardcoded API URL found - should use environment variables", 'WARNING', i)
            
            # Check for proper error handling in API calls
            if 'fetch' in content or 'axios' in content:
                if 'catch' not in content:
                    self.log_issue('NO_ERROR_HANDLING', file_path,
                                 f"API calls without proper error handling")
            
            # Check for authorization header handling
            if 'Authorization' in content:
                auth_patterns = [
                    r'Authorization["\']:\s*["\']Bearer\s*\+',
                    r'Authorization["\']:\s*`Bearer\s*\$\{'
                ]
                
                has_proper_auth = any(re.search(pattern, content) for pattern in auth_patterns)
                if not has_proper_auth:
                    self.log_issue('IMPROPER_AUTH_HEADER', file_path,
                                 f"Authorization header may not be properly formatted", 'WARNING')
            
        except Exception as e:
            self.log_issue('FILE_READ_ERROR', file_path, f"Could not read file: {str(e)}")
    
    def scan_component_security(self):
        """Scan React components for security issues"""
        print("⚛️ Scanning Component Security...")
        
        # Find all component files
        components_path = self.frontend_path / 'components'
        if components_path.exists():
            for component_file in components_path.rglob('*.tsx'):
                self.analyze_component_file(component_file)
    
    def analyze_component_file(self, file_path):
        """Analyze React component files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # Check for dangerouslySetInnerHTML usage
            if 'dangerouslySetInnerHTML' in content:
                for i, line in enumerate(lines, 1):
                    if 'dangerouslySetInnerHTML' in line:
                        self.log_issue('XSS_RISK', file_path,
                                     f"dangerouslySetInnerHTML usage - potential XSS risk", 'ERROR', i)
            
            # Check for eval() usage
            if 'eval(' in content:
                for i, line in enumerate(lines, 1):
                    if 'eval(' in line:
                        self.log_issue('CODE_INJECTION', file_path,
                                     f"eval() usage - code injection risk", 'ERROR', i)
            
            # Check for localStorage usage without proper validation
            if 'localStorage' in content:
                for i, line in enumerate(lines, 1):
                    if 'localStorage.getItem' in line and 'JSON.parse' in line:
                        if 'try' not in content or 'catch' not in content:
                            self.log_issue('UNSAFE_JSON_PARSE', file_path,
                                         f"JSON.parse without try-catch - potential crash", 'WARNING', i)
            
        except Exception as e:
            self.log_issue('FILE_READ_ERROR', file_path, f"Could not read file: {str(e)}")
    
    def check_environment_configuration(self):
        """Check environment configuration security"""
        print("🔧 Checking Environment Configuration...")
        
        env_files = ['.env', '.env.local', '.env.development', '.env.production']
        
        for env_file in env_files:
            env_path = Path(env_file)
            if env_path.exists():
                try:
                    with open(env_path, 'r') as f:
                        content = f.read()
                    
                    # Check for exposed secrets in environment files
                    if any(secret in content.lower() for secret in ['password', 'secret', 'private_key']):
                        self.log_issue('ENV_SECRETS', env_file,
                                     f"Potential secrets in environment file")
                    
                    # Check for production URLs in development
                    if 'development' in env_file and any(prod in content.lower() for prod in ['prod', 'production']):
                        self.log_issue('PROD_IN_DEV', env_file,
                                     f"Production URLs in development environment", 'WARNING')
                
                except Exception as e:
                    self.log_issue('FILE_READ_ERROR', env_file, f"Could not read environment file: {str(e)}")
    
    def generate_security_report(self):
        """Generate comprehensive security report"""
        print("\n" + "=" * 80)
        print("🔒 FRONTEND SECURITY AUDIT REPORT")
        print("=" * 80)
        
        print(f"Total Security Issues: {len(self.issues)}")
        print(f"Total Warnings: {len(self.warnings)}")
        
        if self.issues:
            print("\n🚨 CRITICAL SECURITY ISSUES:")
            for issue in self.issues:
                line_info = f" (Line {issue['line']})" if issue['line'] else ""
                print(f"  [{issue['category']}] {issue['file']}{line_info}")
                print(f"    {issue['message']}")
        
        if self.warnings:
            print("\n⚠️ SECURITY WARNINGS:")
            for warning in self.warnings:
                line_info = f" (Line {warning['line']})" if warning['line'] else ""
                print(f"  [{warning['category']}] {warning['file']}{line_info}")
                print(f"    {warning['message']}")
        
        # Security recommendations
        print("\n📋 SECURITY RECOMMENDATIONS:")
        print("  1. Use environment variables for all API URLs and configuration")
        print("  2. Implement proper error handling for all API calls")
        print("  3. Use permission utility functions instead of hardcoded role checks")
        print("  4. Validate and sanitize all user inputs")
        print("  5. Implement proper token refresh mechanisms")
        print("  6. Use HTTPS in production environments")
        print("  7. Implement Content Security Policy (CSP) headers")
        print("  8. Regular security audits and dependency updates")
        
        print("=" * 80)
        
        return len(self.issues) == 0
    
    def run_full_audit(self):
        """Run complete frontend security audit"""
        print("🔒 Starting Frontend Security Audit...")
        print("-" * 60)
        
        if not self.frontend_path.exists():
            print(f"❌ Frontend path not found: {self.frontend_path}")
            return False
        
        self.scan_authentication_patterns()
        self.scan_permission_patterns()
        self.scan_api_security_patterns()
        self.scan_component_security()
        self.check_environment_configuration()
        
        success = self.generate_security_report()
        
        if success:
            print("✅ Frontend security audit PASSED")
        else:
            print("❌ Frontend security audit FAILED")
        
        return success


def main():
    """Main function to run frontend security audit"""
    audit = FrontendSecurityAudit()
    success = audit.run_full_audit()
    
    import sys
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
