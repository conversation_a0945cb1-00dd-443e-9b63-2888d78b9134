import React from 'react';
import { View, Text } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info' | 'destructive' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  className = '',
}) => {
  const { isDark } = useTheme();

  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'bg-primary/10 border-primary/20';
      case 'warning':
        return 'bg-muted border-border';
      case 'error':
      case 'destructive':
        return 'bg-destructive/10 border-destructive/20';
      case 'info':
        return 'bg-primary/10 border-primary/20';
      case 'secondary':
        return 'bg-muted border-border';
      default:
        return 'bg-muted border-border';
    }
  };

  const getTextStyles = () => {
    const sizeStyles = {
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base',
    }[size];

    const variantTextStyles = {
      success: 'text-primary',
      warning: 'text-muted-foreground',
      error: 'text-destructive',
      destructive: 'text-destructive',
      info: 'text-primary',
      secondary: 'text-foreground',
      default: 'text-foreground',
    }[variant];

    return `font-medium ${sizeStyles} ${variantTextStyles}`;
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1';
      case 'md':
        return 'px-3 py-1';
      case 'lg':
        return 'px-4 py-2';
      default:
        return 'px-3 py-1';
    }
  };

  return (
    <View className={`rounded-full border ${getVariantStyles()} ${getSizeStyles()} ${className}`}>
      <Text className={getTextStyles()}>
        {children}
      </Text>
    </View>
  );
};

export default Badge;
