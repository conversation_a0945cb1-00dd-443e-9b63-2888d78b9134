#!/usr/bin/env python3
"""
HMS Security Audit Execution Script
Quick execution script for running comprehensive HMS security audit
"""

import os
import sys
import subprocess
from pathlib import Path


def print_banner():
    """Print audit banner"""
    print("=" * 80)
    print("🔒 HMS COMPREHENSIVE SECURITY AUDIT")
    print("=" * 80)
    print("Enterprise-grade security analysis for Hospital Management System")
    print("Analysis Level: 30-year experienced developer standards")
    print("=" * 80)


def check_environment():
    """Check if environment is properly set up"""
    print("🔍 Checking Environment...")
    
    # Check if we're in the right directory
    if not Path("backend").exists():
        print("❌ Error: Please run this script from the HMS root directory")
        return False
    
    if not Path("frontend").exists():
        print("❌ Error: Frontend directory not found")
        return False
    
    # Check if Django is available
    try:
        import django
        print(f"✅ Django {django.get_version()} found")
    except ImportError:
        print("❌ Error: Django not installed")
        return False
    
    # Check if backend dependencies are available
    backend_path = Path("backend")
    if not (backend_path / "manage.py").exists():
        print("❌ Error: Django manage.py not found in backend directory")
        return False
    
    print("✅ Environment check passed")
    return True


def run_backend_security_audit():
    """Run backend security audit"""
    print("\n🔐 Running Backend Security Audit...")
    print("-" * 60)
    
    try:
        # Change to backend directory
        os.chdir("backend")
        
        # Run comprehensive security audit
        result = subprocess.run([
            sys.executable, "run_comprehensive_security_audit.py"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        # Change back to root directory
        os.chdir("..")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error running backend security audit: {str(e)}")
        os.chdir("..")  # Ensure we're back in root directory
        return False


def run_frontend_security_audit():
    """Run frontend security audit"""
    print("\n🌐 Running Frontend Security Audit...")
    print("-" * 60)
    
    try:
        result = subprocess.run([
            sys.executable, "frontend_security_audit.py"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Error running frontend security audit: {str(e)}")
        return False


def generate_summary_report(backend_success, frontend_success):
    """Generate summary report"""
    print("\n" + "=" * 80)
    print("🔒 HMS SECURITY AUDIT SUMMARY")
    print("=" * 80)
    
    total_tests = 2
    passed_tests = sum([backend_success, frontend_success])
    
    print(f"Total Audit Components: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\nDETAILED RESULTS:")
    print(f"  Backend Security Audit: {'✅ PASSED' if backend_success else '❌ FAILED'}")
    print(f"  Frontend Security Audit: {'✅ PASSED' if frontend_success else '❌ FAILED'}")
    
    print("\n📋 NEXT STEPS:")
    
    if passed_tests == total_tests:
        print("🎉 ALL SECURITY AUDITS PASSED!")
        print("✅ HMS application meets enterprise security standards")
        print("✅ Ready for production deployment")
        print("\nRecommended actions:")
        print("  1. Review the detailed security report")
        print("  2. Deploy security fixes to staging environment")
        print("  3. Conduct final testing in staging")
        print("  4. Deploy to production with monitoring")
    else:
        print("🚨 SECURITY ISSUES DETECTED!")
        print("⚠️ DO NOT DEPLOY TO PRODUCTION UNTIL ALL ISSUES ARE RESOLVED!")
        print("\nRequired actions:")
        
        if not backend_success:
            print("  - Address backend security vulnerabilities")
            print("  - Fix role-based access control issues")
            print("  - Resolve database integrity problems")
        
        if not frontend_success:
            print("  - Fix frontend security issues")
            print("  - Address authentication vulnerabilities")
            print("  - Resolve permission system inconsistencies")
        
        print("  - Re-run security audit after fixes")
    
    print("\n📄 DETAILED REPORTS:")
    print("  - Backend: Check backend/run_comprehensive_security_audit.py output")
    print("  - Frontend: Check frontend_security_audit.py output")
    print("  - Comprehensive: See HMS_COMPREHENSIVE_SECURITY_ANALYSIS_REPORT.md")
    
    print("=" * 80)
    
    return passed_tests == total_tests


def main():
    """Main execution function"""
    print_banner()
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Run security audits
    backend_success = run_backend_security_audit()
    frontend_success = run_frontend_security_audit()
    
    # Generate summary
    overall_success = generate_summary_report(backend_success, frontend_success)
    
    # Exit with appropriate code
    sys.exit(0 if overall_success else 1)


if __name__ == "__main__":
    main()
