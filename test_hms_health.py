#!/usr/bin/env python3
"""
HMS Application Health Check
Comprehensive testing script for all user roles and functionality
"""

import os
import sys
import json
import requests
import time
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

class HMSHealthChecker:
    def __init__(self):
        self.base_url = 'http://127.0.0.1:8000/api'
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests_passed': 0,
            'tests_failed': 0,
            'issues': [],
            'role_tests': {}
        }
        self.admin_token = None
        self.test_users = {}

    def log_result(self, test_name, success, message=""):
        """Log test result"""
        if success:
            self.test_results['tests_passed'] += 1
            print(f"[PASS] {test_name}")
        else:
            self.test_results['tests_failed'] += 1
            self.test_results['issues'].append(f"{test_name}: {message}")
            print(f"[FAIL] {test_name}: {message}")

    def test_backend_connectivity(self):
        """Test if backend is running and accessible"""
        try:
            # Test the login endpoint with a GET request (should return 405 Method Not Allowed)
            response = requests.get(f"{self.base_url}/auth/login/", timeout=5)
            if response.status_code in [200, 405]:  # 405 Method Not Allowed is OK for connectivity
                self.log_result("Backend Connectivity", True)
                return True
            else:
                self.log_result("Backend Connectivity", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("Backend Connectivity", False, str(e))
            return False

    def test_authentication_endpoints(self):
        """Test authentication endpoints"""
        # Test login endpoint
        try:
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            response = requests.post(f"{self.base_url}/auth/login/", json=login_data, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if 'tokens' in data and 'access' in data['tokens']:
                    self.admin_token = data['tokens']['access']
                    self.log_result("Admin Login", True)
                    return True
                else:
                    self.log_result("Admin Login", False, "Invalid response format")
                    return False
            else:
                self.log_result("Admin Login", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("Admin Login", False, str(e))
            return False

    def test_role_based_access(self):
        """Test role-based access control"""
        if not self.admin_token:
            self.log_result("Role-Based Access", False, "No admin token available")
            return False

        headers = {'Authorization': f'Bearer {self.admin_token}'}
        
        # Test admin endpoints
        endpoints_to_test = [
            ('/users/', 'User Management'),
            ('/patients/', 'Patient Management'),
            ('/appointments/', 'Appointment Management'),
        ]

        for endpoint, name in endpoints_to_test:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", headers=headers, timeout=5)
                if response.status_code in [200, 201]:
                    self.log_result(f"Admin Access - {name}", True)
                else:
                    self.log_result(f"Admin Access - {name}", False, f"Status: {response.status_code}")
            except Exception as e:
                self.log_result(f"Admin Access - {name}", False, str(e))

    def test_permission_system(self):
        """Test the new permission system"""
        try:
            # Import Django and test permission utils
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
            import django
            django.setup()
            
            from users.models import User
            from hms.common_utils import PermissionUtils
            
            # Test admin user permissions
            admin_users = User.objects.filter(role='admin')
            if admin_users.exists():
                admin_user = admin_users.first()
                
                # Test permission methods
                tests = [
                    (PermissionUtils.has_admin_access(admin_user), "Admin Access Check"),
                    (PermissionUtils.has_hms_admin_access(admin_user), "HMS Admin Access Check"),
                    (PermissionUtils.user_can_create_users(admin_user), "User Creation Permission"),
                    (PermissionUtils.user_can_manage_system_settings(admin_user), "System Settings Permission"),
                ]
                
                for result, test_name in tests:
                    self.log_result(f"Permission System - {test_name}", result)
            else:
                self.log_result("Permission System", False, "No admin users found")
                
        except Exception as e:
            self.log_result("Permission System", False, str(e))

    def test_database_consistency(self):
        """Test database consistency"""
        try:
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
            import django
            django.setup()
            
            from users.models import User
            from patient_management.models import Patient
            
            # Check user roles
            role_counts = {}
            for role_choice in User.Role.choices:
                role = role_choice[0]
                count = User.objects.filter(role=role).count()
                role_counts[role] = count
            
            self.log_result("Database - User Roles", True, f"Roles: {role_counts}")
            
            # Check patient consistency
            patient_count = Patient.objects.count()
            patient_user_count = User.objects.filter(role='patient').count()
            
            if patient_count == patient_user_count:
                self.log_result("Database - Patient Consistency", True)
            else:
                self.log_result("Database - Patient Consistency", False, 
                              f"Patients: {patient_count}, Patient Users: {patient_user_count}")
                
        except Exception as e:
            self.log_result("Database Consistency", False, str(e))

    def test_frontend_files(self):
        """Test frontend file integrity"""
        critical_files = [
            'frontend/src/store/slices/authSlice.ts',
            'frontend/src/shared/utils/auth.ts',
            'frontend/src/shared/utils/permissions.ts',
            'frontend/src/shared/utils/constants.ts',
            'frontend/src/components/dashboard/RoleDashboard.tsx',
        ]
        
        for file_path in critical_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if len(content) > 0:
                            self.log_result(f"Frontend File - {os.path.basename(file_path)}", True)
                        else:
                            self.log_result(f"Frontend File - {os.path.basename(file_path)}", False, "Empty file")
                except Exception as e:
                    self.log_result(f"Frontend File - {os.path.basename(file_path)}", False, str(e))
            else:
                self.log_result(f"Frontend File - {os.path.basename(file_path)}", False, "File not found")

    def test_token_consistency(self):
        """Test token storage consistency in frontend"""
        files_to_check = [
            'frontend/src/utils/api.ts',
            'frontend/src/store/slices/authSlice.ts',
            'frontend/src/shared/utils/auth.ts',
        ]
        
        inconsistent_files = []
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # Check for inconsistent token storage
                        if "localStorage.getItem('access_token')" in content:
                            inconsistent_files.append(file_path)
                except Exception as e:
                    self.log_result(f"Token Consistency - {os.path.basename(file_path)}", False, str(e))
        
        if inconsistent_files:
            self.log_result("Token Storage Consistency", False, f"Inconsistent files: {inconsistent_files}")
        else:
            self.log_result("Token Storage Consistency", True)

    def run_comprehensive_test(self):
        """Run all tests"""
        print("HMS Application Health Check")
        print("=" * 50)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        # Run tests in order
        print("Testing Backend Connectivity...")
        backend_ok = self.test_backend_connectivity()

        print("\nTesting Authentication...")
        if backend_ok:
            self.test_authentication_endpoints()

        print("\nTesting Role-Based Access...")
        self.test_role_based_access()

        print("\nTesting Permission System...")
        self.test_permission_system()

        print("\nTesting Database Consistency...")
        self.test_database_consistency()

        print("\nTesting Frontend Files...")
        self.test_frontend_files()

        print("\nTesting Token Consistency...")
        self.test_token_consistency()

        # Summary
        print("\nTEST SUMMARY")
        print("=" * 50)
        total_tests = self.test_results['tests_passed'] + self.test_results['tests_failed']
        success_rate = (self.test_results['tests_passed'] / total_tests * 100) if total_tests > 0 else 0

        print(f"Total Tests: {total_tests}")
        print(f"Passed: {self.test_results['tests_passed']}")
        print(f"Failed: {self.test_results['tests_failed']}")
        print(f"Success Rate: {success_rate:.1f}%")

        if self.test_results['issues']:
            print(f"\nISSUES FOUND ({len(self.test_results['issues'])})")
            print("-" * 30)
            for issue in self.test_results['issues']:
                print(f"  - {issue}")
        else:
            print("\nALL TESTS PASSED!")

        # Save results
        with open('hms_health_report.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)

        print(f"\nDetailed report saved to: hms_health_report.json")

        return self.test_results['tests_failed'] == 0

if __name__ == "__main__":
    checker = HMSHealthChecker()
    success = checker.run_comprehensive_test()
    sys.exit(0 if success else 1)
